import { type Message } from "@braintrust/typespecs";
import * as <PERSON><PERSON><PERSON> from "../../yaml";
import { DEFAULT_MODEL, BASE_PROMPT } from "./system-prompt";
import {
  createConditionalJsonProcessingStream,
  type ParallelToolCall,
  parallelToolCallSchema,
  StreamingError,
  StreamingTextDelta,
  StreamingToolDelta,
} from "../../functions";
import {
  BackgroundLogEvent,
  BT_PARENT,
  IS_MERGE_FIELD,
  SpanComponentsV3,
  SpanTypeAttribute,
} from "braintrust/util";
import {
  BraintrustState,
  BraintrustStream,
  LazyValue,
  newId,
  NOOP_SPAN,
  Span,
  startSpan,
  traced,
  type BraintrustStreamChunk,
} from "braintrust";
import { z } from "zod";
import {
  continueExecutionToolResult,
  GetSummaryToolResult,
  GetResultsToolResult,
  type ToolManager,
  type ToolName,
  validToolName,
} from "../tools";
import {
  Chat<PERSON>ompletionCreateParams,
  ChatCompletionCreateParamsBase,
  ChatCompletionMessageParam,
  ChatCompletionTool,
} from "openai/resources/chat/completions";

export type ModelParams = Pick<
  ChatCompletionCreateParamsBase,
  | "temperature"
  | "max_tokens"
  | "top_p"
  | "frequency_penalty"
  | "presence_penalty"
>;

export class UserRejectedError extends Error {
  constructor(message: string | undefined) {
    super("User rejected the tool call" + (message ? `: ${message}` : ""));
    this.name = "UserRejectedError";
  }
}

export const DEFAULT_MAX_ROUNDTRIPS = 10;

export interface ChatContextParams {
  chatLogger?: ChatLogger;
  consoleLogger?: ConsoleLogger;

  cacheMode?: "always" | "never";
  displayError?: (error: Error) => void;

  model?: string;
  allowed_tools?: ToolName[];
  defaultSystemPrompt?: string;
  messages?: Message[];
  modelParams?: ModelParams;
  maxRoundtrips?: number;
  retryCount?: number;
  onAddMessage?: (message: Message) => void;
}

export type BaseChatContextParams = ChatContextParams & {
  orgName: string | undefined;
  loggingRoot: SpanComponentsV3 | undefined;
  loggingState: BraintrustState;
  loggingMetadata: LoggingMetadata;
  tags?: string[];
  enableLogging: boolean;
  tools: ToolManager;
};

export interface LoggingMetadata {
  user_id: string | undefined;
  email: string | undefined;
  project_id: string | undefined;
  org_id: string | undefined;
}

export interface ToolExecution {
  tool_call_id: string;
  function_name: string;
  output: unknown;
  error: Error | undefined;
}

export interface ChatLogger {
  onTextDelta?: (text: StreamingTextDelta) => void;
  onToolDelta?: (args: StreamingToolDelta) => void;
  onToolExecution?: (execution: ToolExecution) => void;
  onError?: (error: StreamingError) => void;
  flush(): void;
}

export interface ConsoleLogger {
  write(message: string): void;
  flush(): void;
}

export interface ChatMetrics {
  toolCalls: number;
  toolCallsByType: Record<string, number>;
  errors: number;
  completed: number;
  // This happens every time the model calls tools that it's not supposed to.
  invalidToolCalls: number;
}

// One for the very latest and one for the second to last. This ensures
// the current message can be reused next time.
const MESSAGE_BREAKPOINTS = 2;

export class ChatContext {
  private orgName: string | undefined;
  // The root is the thing under which everything should be nested. This chat context maintains
  // its own single parent, which is nested under the root
  private loggingRoot: SpanComponentsV3 | undefined;
  private loggingParent: SpanComponentsV3 | undefined;
  private loggingState: BraintrustState;

  private loggingMetadata: LoggingMetadata;
  private tags: string[] = [];
  private enableLogging: boolean;
  private updateEndTime: () => void = () => {};
  private initializedLogger: "pending" | "success" | "failure" = "pending";
  private tools: ToolManager;

  // The chatLogger allows you to capture structured messages, whereas the console logger
  // is a simplified format that is useful for debugging.
  private chatLogger: ChatLogger | undefined;
  private consoleLogger: ConsoleLogger | undefined;
  private displayError: (error: Error) => void;

  private cacheMode: undefined | "always" | "never";
  private readonly defaultSystemPrompt: string;
  private messages: Message[];
  private messagesHashSet: Set<string>;
  private onAddMessage: ((message: Message) => void) | undefined;
  private breakpoints: number[] = []; // Tracks the 3 breakpoints other than system prompt
  private model: string;
  private allowed_tools: ToolName[];
  private modelParams: ModelParams;
  private maxRoundtrips: number;
  private retryCount: number;

  private lastTurnSnapshot:
    | { message: string; snapshot: ChatSnapshot }
    | undefined = undefined;
  private lastTurnOutput: CompositeValue | undefined = undefined;

  private pendingTools: ParallelToolCall[] = [];
  public metrics: ChatMetrics = {
    toolCalls: 0,
    toolCallsByType: {},
    errors: 0,
    completed: 0,
    invalidToolCalls: 0,
  };

  constructor(args: BaseChatContextParams) {
    this.orgName = args.orgName;
    this.loggingRoot = args.loggingRoot;
    this.loggingState = args.loggingState;
    this.loggingMetadata = args.loggingMetadata;
    this.tags = args.tags ?? [];
    this.enableLogging = args.enableLogging;
    this.tools = args.tools;

    this.cacheMode = args.cacheMode;
    this.model = args.model ?? DEFAULT_MODEL;
    this.allowed_tools = args.allowed_tools ?? args.tools.list();

    this.defaultSystemPrompt = args.defaultSystemPrompt ?? BASE_PROMPT;
    // Shallow copy to avoid any immer-related mutation problems, when this is used on the client
    this.messages = [...(args.messages ?? [])];
    this.messagesHashSet = new Set(
      (args.messages ?? []).map((m) => JSON.stringify(m)),
    );
    this.onAddMessage = args.onAddMessage;
    this.modelParams = args.modelParams ?? {};
    this.maxRoundtrips = args.maxRoundtrips ?? DEFAULT_MAX_ROUNDTRIPS;
    this.retryCount = args.retryCount ?? 3;
    this.consoleLogger = args.consoleLogger;
    this.chatLogger = args.chatLogger;
    this.displayError = args.displayError ?? console.error;
  }

  protected async logData(rows: BackgroundLogEvent[]): Promise<void> {
    const bgLogger = this.loggingState.bgLogger();
    bgLogger.log(rows.map((r) => new LazyValue(() => Promise.resolve(r))));
    await bgLogger.flush();
  }

  public addMessage(message: Message) {
    this.messages.push(message);
    this.messagesHashSet.add(JSON.stringify(message));
    this.onAddMessage?.(message);
  }

  public updateMessagesWithExternal(messages: Message[]) {
    for (const message of messages) {
      const messageHash = JSON.stringify(message);
      if (!this.messagesHashSet.has(messageHash)) {
        this.messages.push(message);
        this.messagesHashSet.add(messageHash);
      }
    }
  }

  public reconfigure(params: {
    model?: string;
    allowed_tools?: ToolName[];
    modelParams?: ModelParams;
  }): void {
    if (params.model !== undefined && params.model !== this.model) {
      this.model = params.model;
    }

    if (params.modelParams !== undefined) {
      this.modelParams = params.modelParams;
    }

    if (params.allowed_tools !== undefined) {
      const newTools = params.allowed_tools;
      const currentTools = this.allowed_tools;

      if (
        newTools.length !== currentTools.length ||
        !newTools.every((tool) => currentTools.includes(tool)) ||
        !currentTools.every((tool) => newTools.includes(tool))
      ) {
        this.allowed_tools = [...newTools];
      }
    }
  }

  public async generateResponse(opts?: {
    span: Span;
    disableTools?: boolean;
    abortController?: AbortController;
  }): Promise<BraintrustStream> {
    if (this.pendingTools.length > 0) {
      throw new Error(
        `Cannot generate response with pending tools (${this.pendingTools
          .map((t) => t.function_name)
          .join(", ")})`,
      );
    }

    const { abortController } = opts ?? {};
    const tools: Array<ChatCompletionTool> | undefined = opts?.disableTools
      ? [this.tools.getOpenAITool("continue_execution")]
      : this.allowed_tools.length > 0
        ? this.allowed_tools
            .filter((name) => name !== "continue_execution")
            .map((name) => this.tools.getOpenAITool(name))
        : undefined;

    const allowedToolNames = (tools ?? []).map((t) => t.function.name);

    this.breakpoints.push(this.messages.length - 1);
    if (this.breakpoints.length > MESSAGE_BREAKPOINTS) {
      this.breakpoints.shift();
    }

    const messages: Message[] = [
      {
        role: "system" as const,
        content: [
          {
            type: "text",
            text: this.defaultSystemPrompt,
            cache_control: {
              type: "ephemeral",
            },
          },
        ],
      },
      ...this.messages.map((message: Message, idx: number): Message => {
        if (this.breakpoints.includes(idx) && message.content) {
          const content = upgradeContent(message.content);
          if (content && content[content.length - 1].type === "text") {
            // @ts-ignore
            content[content.length - 1].cache_control = {
              type: "ephemeral",
            };
          }
          // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
          return {
            ...message,
            content,
          } as Message;
        } else {
          return message;
        }
      }),
    ];
    const params: ChatCompletionCreateParams = {
      ...this.modelParams,
      tools,
      messages: messages.map(braintrustMessageToOpenaiMessage),
      model: this.model,
      stream: true,
      stream_options: {
        include_usage: true,
      },
    };
    const parent = await opts?.span.export();

    let response: Response | undefined;
    let lastError: Error | undefined;

    for (let attempt = 1; attempt <= this.retryCount; attempt++) {
      try {
        response = await this.loggingState
          .proxyConn()
          .post("/v1/proxy/auto", JSON.stringify(params), {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              ...(this.cacheMode ? { "x-bt-use-cache": this.cacheMode } : {}),
              ...(this.orgName ? { "x-bt-org-name": this.orgName } : {}),
              ...(parent ? { [BT_PARENT]: parent } : {}),
            },
            ...(abortController ? { signal: abortController.signal } : {}),
          });

        // If successful, break out of retry loop
        break;
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));

        // Only retry on type errors
        if (error instanceof TypeError && attempt < this.retryCount) {
          await new Promise((resolve) =>
            setTimeout(resolve, Math.pow(2, attempt - 1) * 1000),
          ); // Exponential backoff: 1s, 2s
          continue;
        }

        // For non-type errors or if this was the last attempt, throw
        throw error;
      }
    }

    if (!response) {
      throw lastError || new Error("Failed to get response after retries");
    }

    if (!response.ok) {
      throw new Error(
        `Failed to generate response (${response.status}: ${response.statusText}): ${await response.text()}`,
      );
    }

    let hasFailed = false;
    if (abortController) {
      abortController.signal.addEventListener("abort", () => {
        hasFailed = true;
      });
    }

    const body = response.body;
    if (!body) {
      throw new Error("No body");
    }

    let stream = new BraintrustStream(
      body.pipeThrough(
        createConditionalJsonProcessingStream({
          mode: "parallel",
          isFail: () => hasFailed,
          isJSONResponse: false,
          includeToolCallIds: true,
          onTextDelta: (text) => {
            this.chatLogger?.onTextDelta?.(text);
          },
          onToolDelta: (toolDelta) => {
            this.chatLogger?.onToolDelta?.(toolDelta);
          },
          onError: (error) => {
            this.chatLogger?.onError?.(error);
          },
          onFinish: () => {
            this.chatLogger?.flush();
          },
        }),
      ),
      { signal: abortController?.signal },
    );

    if (this.consoleLogger) {
      stream = new BraintrustStream(
        stream
          .toReadableStream()
          .pipeThrough(wrapConsoleLogStream(this.consoleLogger)),
        { signal: abortController?.signal },
      );
    }

    stream = new BraintrustStream(
      stream
        .toReadableStream()
        .pipeThrough(this.captureAssistantMessageStream(allowedToolNames)),
      { signal: abortController?.signal },
    );

    return stream;
  }

  public async turn(
    message: string,
    opts?: { abortController?: AbortController },
  ) {
    if (this.pendingTools.length > 0) {
      this.cleanupPendingTools({
        type: "user_rejection",
        reason: "User rejected the tool call",
      });
    }
    this.lastTurnSnapshot = {
      message,
      snapshot: await this.snapshot(),
    };

    return await traced(
      async (span) => {
        this.addMessage({
          role: "user",
          content: message,
        });

        try {
          const stream = await this.generateResponse({
            span,
            ...opts,
          });
          const result = await finalCompositeValue(stream);
          this.lastTurnOutput =
            (await this.executePendingTools(span, opts?.abortController)) ??
            result;
          const { errors, ...ret } = this.lastTurnOutput;
          this.updateEndTime();
          span.log({
            output: ret,
            error: errors.length > 0 ? new Error(errors.join("\n")) : undefined,
          });
          return ret;
        } catch (e) {
          if (e instanceof DOMException && e.name === "AbortError") {
            this.cleanupPendingTools({
              type: "abort",
              reason: "Tool call cancelled due to abort operation",
            });
          }
          throw e;
        }
      },
      {
        state: this.loggingState,
        name: "turn",
        type: SpanTypeAttribute.TASK,
        parent: this.getLoggingParent(message)?.toStr(),
        event: {
          input: message,
        },
      },
    );
  }

  private cleanupPendingTools({
    type,
    reason,
  }: {
    type: "abort" | "user_rejection";
    reason: string;
  }) {
    if (this.pendingTools.length > 0) {
      this.pendingTools = [];
    }
    if (this.messages.length > 0) {
      let lastAssistantMessageWithToolCalls = null;
      for (let i = this.messages.length - 1; i >= 0; i--) {
        const message = this.messages[i];
        if (
          message.role === "assistant" &&
          message.tool_calls &&
          message.tool_calls.length > 0
        ) {
          lastAssistantMessageWithToolCalls = message;
          break;
        }
      }

      if (lastAssistantMessageWithToolCalls) {
        const toolCallsMadeByLastMessage =
          lastAssistantMessageWithToolCalls.tool_calls ?? [];
        const messagesAfterLastAssistantMessage = this.messages.slice(
          this.messages.indexOf(lastAssistantMessageWithToolCalls) + 1,
        );
        const unresolvedToolCalls = toolCallsMadeByLastMessage.filter(
          (tc) =>
            !messagesAfterLastAssistantMessage.some(
              (msg) => msg.role === "tool" && msg.tool_call_id === tc.id,
            ),
        );

        if (unresolvedToolCalls.length > 0) {
          for (const toolCall of unresolvedToolCalls) {
            this.addMessage({
              role: "tool",
              tool_call_id: toolCall.id,
              content: reason,
            });

            const toolOutputForLogging = {
              tool_call_id: toolCall.id,
              function_name:
                toolCall.function?.name ||
                "unknown_function_due_to_cancellation",
              output: null,
              error:
                type === "abort"
                  ? new DOMException(reason, "AbortError")
                  : new UserRejectedError(reason),
            };
            if (this.consoleLogger) {
              this.consoleLogger.write(
                JSON.stringify(toolOutputForLogging) + "\n",
              );
              this.consoleLogger.flush();
            }
            this.chatLogger?.onToolExecution?.(toolOutputForLogging);
          }
        }
      }
    }
  }

  // NOTE: We should generalize the tool calling interface to either return the results of a tool
  // or require the user to review/confirm the action. It'll be some weird async interface.
  private async executePendingTools(
    span: Span,
    abortController?: AbortController,
  ): Promise<CompositeValue | undefined> {
    let result: CompositeValue | undefined = undefined;
    let maxRoundtrips = this.maxRoundtrips;
    // The extra +1 lets us call continue_execution at the end of the loop.
    for (let i = 0; i < maxRoundtrips + 1; i++) {
      if (this.pendingTools.length === 0) {
        break;
      }

      // Iterate over a copy, so that we can remove pending tools as they complete
      const toolsToExecute = [...this.pendingTools];

      const toolResults = await Promise.all(
        toolsToExecute.map(async (tool) => {
          let result;
          let error: Error | undefined;
          try {
            this.metrics.toolCalls++;
            this.metrics.toolCallsByType[tool.function_name] =
              (this.metrics.toolCallsByType[tool.function_name] ?? 0) + 1;
            result = await span.traced(
              async (span) => {
                const result = await this.tools.executeTool(
                  validToolName(tool.function_name),
                  tool.arguments,
                  span,
                  abortController,
                );
                span.log({
                  output: result,
                });
                return result;
              },
              {
                event: {
                  input: tool.arguments,
                },
                name: tool.function_name,
                type: SpanTypeAttribute.TOOL,
              },
            );
            this.metrics.completed++;
          } catch (e) {
            this.metrics.errors++;

            if (e instanceof DOMException && e.name === "AbortError") {
              throw e;
            }

            if (e instanceof Error) {
              error = e;
            }

            result = {
              error: e instanceof Error ? e.message : `${e}`,
            };
          }

          const toolOutputForLogging = {
            tool_call_id: tool.tool_call_id, // from the 'tool' object in the loop
            function_name: tool.function_name, // from the 'tool' object
            output: result, // 'result' is the direct output of the current tool execution
            error,
          };
          if (this.consoleLogger) {
            this.consoleLogger.write(
              JSON.stringify(toolOutputForLogging) + "\n",
            );
            this.consoleLogger.flush();
          }
          this.chatLogger?.onToolExecution?.(toolOutputForLogging);

          this.pendingTools = this.pendingTools.filter(
            (pendingTool) => pendingTool.tool_call_id !== tool.tool_call_id,
          );
          const toolMessage: Message = {
            role: "tool",
            content: jsonToYaml(result ?? null),
            tool_call_id: tool.tool_call_id,
          };
          this.addMessage(toolMessage);

          return {
            tool_call_id: tool.tool_call_id,
            result,
          };
        }),
      );

      if (
        toolsToExecute.length === 1 &&
        toolsToExecute[0].function_name === "continue_execution"
      ) {
        const result = continueExecutionToolResult.parse(toolResults[0].result);
        maxRoundtrips += result.allowed ? this.maxRoundtrips : 0;
      }

      const disableTools = i === maxRoundtrips - 1;
      if (disableTools) {
        // TODO: We could actually have a single tool call here that allows the LLM to say "continue!", and prompt
        // the user in that case.
        this.addMessage({
          role: "user",
          content:
            "We have run out of iterations, so if you need to perform more actions, let me know clearly. Otherwise, just return a summary of your work.",
        });
      }

      const stream = await this.generateResponse({
        span,
        disableTools,
        abortController,
      });
      result = await finalCompositeValue(stream);
    }

    if (this.pendingTools.length > 0) {
      throw new Error(
        `Pending tools even after max roundtrips (${this.pendingTools
          .map((t) => t.function_name)
          .join(", ")})`,
      );
    }

    return result;
  }

  private getLoggingParent(input: unknown): SpanComponentsV3 | undefined {
    if (
      this.initializedLogger === "failure" ||
      !this.enableLogging ||
      !this.loggingRoot
    ) {
      return undefined;
    } else if (this.initializedLogger === "success") {
      return this.loggingParent;
    }

    const span_id = newId();
    const root_span_id = this.loggingRoot.data.root_span_id ?? span_id;
    const span_parents = this.loggingRoot.data.span_id
      ? [this.loggingRoot.data.span_id]
      : [];
    const id = newId();

    this.loggingParent = new SpanComponentsV3({
      object_type: this.loggingRoot.data.object_type,
      object_id: this.loggingRoot.data.object_id,
      row_id: id,
      root_span_id,
      span_id,
    });

    this.initializedLogger = "success";
    const initializeSpan = async () => {
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
      const spanData = {
        id,
        root_span_id,
        span_id,
        span_parents,
        span_attributes: {
          name: "optimization",
          type: SpanTypeAttribute.TASK,
        },
        input,
        tags: this.tags.length > 0 ? this.tags : undefined,
        metadata: this.loggingMetadata,
        metrics: {
          start: Date.now() / 1000,
        },
        ...this.loggingRoot?.objectIdFields(),
      } as unknown as BackgroundLogEvent;

      try {
        await this.logData([spanData]);
      } catch (e) {
        console.error(e);
        this.initializedLogger = "failure";
        this.displayError(new Error("Failed to initialize logging context"));
      }

      this.updateEndTime = async () => {
        this.logData([
          // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
          {
            id,
            root_span_id,
            span_id,
            span_parents,
            metrics: {
              end: Date.now() / 1000,
            },
            [IS_MERGE_FIELD]: true,
            ...this.loggingRoot?.objectIdFields(),
          } as BackgroundLogEvent,
        ]).catch(console.error);
      };
    };
    initializeSpan().catch(console.error);

    return this.loggingParent;
  }

  private captureAssistantMessageStream(allowedToolNames: string[]) {
    const textChunks: string[] = [];
    const jsonChunks: string[] = [];
    const errors: string[] = [];

    // Capture the global this context to be used in the functions inside the transform stream that doesn't inherit this.
    const ctx = {
      addMessage: this.addMessage.bind(this),
      setPendingTools: (tools: ParallelToolCall[] | undefined) => {
        this.pendingTools = tools ?? [];
      },
      metrics: this.metrics,
      displayError: this.displayError,
    };

    const buildAssistantMessage = () => {
      let toolCalls: ParallelToolCall[] | undefined = undefined;

      if (jsonChunks.length > 0) {
        const toolCallsParsed = z
          .array(parallelToolCallSchema)
          .safeParse(JSON.parse(jsonChunks.join("")));
        if (!toolCallsParsed.success) {
          ctx.displayError(toolCallsParsed.error);
        } else {
          toolCalls = toolCallsParsed.data;
        }
      }

      if (toolCalls) {
        const invalidToolCalls = [];
        const validToolCalls = [];
        for (const toolCall of toolCalls) {
          if (allowedToolNames.includes(toolCall.function_name)) {
            validToolCalls.push(toolCall);
          } else {
            invalidToolCalls.push(toolCall);
          }
        }
        toolCalls = validToolCalls;
        if (invalidToolCalls.length > 0) {
          ctx.metrics.invalidToolCalls += invalidToolCalls.length;
        }

        ctx.setPendingTools(toolCalls);
      }

      const message: Message = {
        role: "assistant",
        content: textChunks.join(""),
        tool_calls:
          toolCalls && toolCalls.length > 0
            ? toolCalls.map((toolCall) => ({
                id: toolCall.tool_call_id,
                type: "function",
                function: {
                  name: toolCall.function_name,
                  arguments: JSON.stringify(toolCall.arguments),
                },
              }))
            : undefined,
      };

      // Only add the message if it has content or tool calls
      if (message.content || message.tool_calls) {
        ctx.addMessage(message);
      }
    };

    return new TransformStream<BraintrustStreamChunk, BraintrustStreamChunk>({
      transform(chunk, controller) {
        switch (chunk.type) {
          case "text_delta":
            textChunks.push(chunk.data);
            break;
          case "json_delta":
            jsonChunks.push(chunk.data);
            break;
          case "error":
            errors.push(chunk.data);
            break;
          default:
            break;
        }

        controller.enqueue(chunk);
      },
      flush(_controller) {
        if (errors.length > 0) {
          ctx.displayError(new Error(errors.join("\n")));
          return;
        }

        buildAssistantMessage();
      },
    });
  }

  private async snapshot(): Promise<ChatSnapshot> {
    // This snapshot assumes you are in the playground or experiments because these two tools only work in those places (not logs). So adding this early return to avoid errors.
    // Once our tools are well - generalized to work even in logs, we can remove this. or we can just add the right tools here.
    const implementedTools = this.tools
      .list()
      .filter((name) => this.allowed_tools.includes(name));
    if (
      !implementedTools.includes("get_summary") ||
      !implementedTools.includes("get_results")
    ) {
      return { summary: [], results: [] };
    }
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    const summary = (await this.tools.executeTool(
      "get_summary",
      {},
      NOOP_SPAN,
    )) as GetSummaryToolResult[];
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    const results = (await Promise.all(
      summary.map((task) =>
        this.tools.executeTool(
          "get_results",
          { index: task.index, numSamples: 10 },
          NOOP_SPAN,
        ),
      ),
    )) as GetResultsToolResult[][];
    return { summary, results };
  }

  public async recordFeedback({
    score,
    comment,
    userId,
    opts,
  }: {
    score: number | null;
    comment: string | undefined;
    userId: string | undefined;
    opts?: {
      span?: Span;
    };
  }): Promise<Span> {
    if (opts?.span) {
      opts.span.logFeedback({
        comment,
        scores: { feedback: score },
      });
      return opts.span;
    }
    const span = startSpan({
      state: this.loggingState,
      name: "feedback",
      type: SpanTypeAttribute.SCORE,
      parent: this.getLoggingParent(null)?.toStr(),
      event: {
        input: this.lastTurnSnapshot,
        output: this.lastTurnOutput,
      },
    });
    try {
      span.logFeedback({
        scores: {
          feedback: score,
        },
        comment,
        metadata: userId ? { user_id: userId } : undefined,
      });
      return span;
    } finally {
      span.end();
    }
  }
}

function braintrustMessageToOpenaiMessage(
  message: Message,
): ChatCompletionMessageParam {
  switch (message.role) {
    case "model":
      throw new Error("Model messages are not supported");
    default:
      // This assumes that we're not passing in messages which contain direct references
      // to attachments. We _could_ dig through the struct recursively to validate that, but
      // I don't think it's worth the effort.
      //
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
      return message as ChatCompletionMessageParam;
  }
}

function wrapConsoleLogStream(consoleLogger: ConsoleLogger) {
  let lastType: undefined | BraintrustStreamChunk["type"] = undefined;
  return new TransformStream<BraintrustStreamChunk, BraintrustStreamChunk>({
    transform(chunk, controller) {
      if (typeof chunk.data === "string") {
        if (
          lastType !== undefined &&
          lastType !== chunk.type &&
          chunk.type !== "done"
        ) {
          consoleLogger.write("\n\n");
        }
        lastType = chunk.type;

        consoleLogger.write(chunk.data);
      } else {
        console.warn(chunk.data);
      }

      controller.enqueue(chunk);
    },
    flush(_controller) {
      consoleLogger.flush();
    },
  });
}

export const jsonToYaml = (json: unknown) => {
  return JSY.dump(json, {
    lineWidth: -1,
    noRefs: true,
    noCompatMode: true,
    sortKeys: true,
  }).replace(/\n$/, ""); // Remove the last newline character
};

export const yamlToJson = (yaml: string) => {
  return JSY.load(yaml);
};

interface CompositeValue {
  text?: string;
  toolCalls?: ParallelToolCall[];
  errors: string[];
}
export async function finalCompositeValue(
  stream: BraintrustStream,
): Promise<CompositeValue> {
  const textChunks: string[] = [];
  const jsonChunks: string[] = [];
  const errors: string[] = [];
  await stream.toReadableStream().pipeTo(
    new WritableStream({
      write(chunk) {
        switch (chunk.type) {
          case "text_delta":
            textChunks.push(chunk.data);
            break;
          case "json_delta":
            jsonChunks.push(chunk.data);
            break;
          case "error":
            errors.push(chunk.data);
            break;
          default:
            break;
        }
      },
    }),
  );

  const json = z
    .array(parallelToolCallSchema)
    .safeParse(
      jsonChunks.length > 0 ? JSON.parse(jsonChunks.join("")) : undefined,
    );
  if (!json.success) {
    errors.push(json.error.message);
  }
  return {
    text: textChunks.length > 0 ? textChunks.join("") : undefined,
    toolCalls: json.success ? json.data : undefined,
    errors,
  };
}

function upgradeContent<T extends Message["content"]>(
  content: T,
): Exclude<T, string> {
  if (typeof content === "string") {
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    return [
      {
        type: "text" as const,
        text: content,
      },
    ] as Exclude<T, string>;
  } else {
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    return content as Exclude<T, string>;
  }
}

interface ChatSnapshot {
  summary: GetSummaryToolResult[];
  results: GetResultsToolResult[][];
}
