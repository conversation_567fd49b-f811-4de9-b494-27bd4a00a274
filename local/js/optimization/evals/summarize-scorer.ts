import { LLMClassifierFromTemplate, Score } from "autoevals";

export const SummaryQualityClassifier = LLMClassifierFromTemplate({
  name: "Summary Quality Classifier",
  model: "claude-3-5-sonnet-latest",
  promptTemplate: `
You are an expert evaluator assessing the quality of a summary of an experiment in Braintrust Platform.
An experiment is an run of evaluation of a task.

Your task is to determine if the generated summary is accurate, clear, and relevant to the experiment.

Quality Standards Overview:

Accuracy: Information should be factually correct and consistent with the experiment results
Clarity: Should be well-structured, easy to follow, and concise
Relevance: Should focus on the most important aspects and avoid unnecessary details

Examples:
Input 1:
Task 1: Unsupervised improvement claude-sonnet-4
[
  {
    "input": "factuality",
    "output": {
      "baseline": {
        "NumericDiff": {
          "avg": 0.596,
          "max": 1,
          "min": 0
        }
      },
      "metrics": {
        "completed": 9,
        "errors": 1,
        "invalidToolCalls": 0,
        "toolCalls": 10,
        "toolCallsByType": {
          "edit_task": 4,
          "get_playground_summary": 1,
          "get_results": 4,
          "run_task": 1
        }
      }
    },
    "error": null,
    "expected": null,
    "tags": null,
    "scores": {
      "avg_improvement_per_edit": 0,
      "no_invalid_tool_calls": 1,
      "score_improvement": 0,
      "tool_success_rate": 0.9
    },
    "metrics": {
      "prompt_tokens": "\"379619000\"",
      "completion_tokens": "\"2099000\"",
      "total_tokens": "\"381718000\"",
      "estimated_cost": 0.50713065,
      "prompt_cache_creation_tokens": 104811,
      "prompt_cached_tokens": 274748
    },
    "metadata": {
      "description": "Check if the output is factually correct",
      "difficulty": "easy"
    }
  },
  {
    "input": "factuality",
    "output": {
      "baseline": {
        "NumericDiff": {
          "avg": 0.633,
          "max": 1,
          "min": 0
        }
      },
      "metrics": {
        "completed": 10,
        "errors": 0,
        "invalidToolCalls": 0,
        "toolCalls": 10,
        "toolCallsByType": {
          "edit_scorers": 1,
          "edit_task": 2,
          "get_available_scorers": 1,
          "get_playground_summary": 1,
          "get_results": 3,
          "run_task": 2
        }
      }
    },
    "error": null,
    "expected": null,
    "tags": null,
    "scores": {
      "avg_improvement_per_edit": 0,
      "no_invalid_tool_calls": 1,
      "score_improvement": 0,
      "tool_success_rate": 1
    },
    "metrics": {
      "prompt_tokens": "\"159020000\"",
      "completion_tokens": "\"1768000\"",
      "total_tokens": "\"160788000\"",
      "estimated_cost": 0.21992280000000003,
      "prompt_cache_creation_tokens": 42184,
      "prompt_cached_tokens": 116776
    },
    "metadata": {
      "description": "Check if the output is factually correct",
      "difficulty": "easy"
    }
  },
  {
    "input": "factuality",
    "output": {
      "baseline": {
        "NumericDiff": {
          "avg": 0.633,
          "max": 1,
          "min": 0
        }
      },
      "metrics": {
        "completed": 11,
        "errors": 0,
        "invalidToolCalls": 0,
        "toolCalls": 11,
        "toolCallsByType": {
          "continue_execution": 1,
          "edit_scorers": 2,
          "edit_task": 2,
          "get_available_scorers": 1,
          "get_playground_summary": 1,
          "get_results": 2,
          "run_task": 2
        }
      }
    },
    "error": null,
    "expected": null,
    "tags": null,
    "scores": {
      "avg_improvement_per_edit": 0,
      "no_invalid_tool_calls": 1,
      "score_improvement": 0,
      "tool_success_rate": 1
    },
    "metrics": {
      "prompt_tokens": "\"249479000\"",
      "completion_tokens": "\"1815000\"",
      "total_tokens": "\"251294000\"",
      "estimated_cost": 0.29286284999999995,
      "prompt_cache_creation_tokens": 55251,
      "prompt_cached_tokens": 194162
    },
    "metadata": {
      "description": "Check if the output is factually correct",
      "difficulty": "easy"
    }
  },
  {
    "input": "factuality",
    "output": {
      "baseline": {
        "NumericDiff": {
          "avg": 0.65,
          "max": 1,
          "min": 0
        }
      },
      "metrics": {
        "completed": 10,
        "errors": 0,
        "invalidToolCalls": 1,
        "toolCalls": 10,
        "toolCallsByType": {
          "edit_task": 3,
          "get_available_scorers": 1,
          "get_playground_summary": 1,
          "get_results": 3,
          "run_task": 2
        }
      }
    },
    "error": null,
    "expected": null,
    "tags": null,
    "scores": {
      "avg_improvement_per_edit": 0.04766666666666667,
      "no_invalid_tool_calls": 0,
      "score_improvement": 0.14300000000000002,
      "tool_success_rate": 1
    },
    "metrics": {
      "prompt_tokens": "\"292627000\"",
      "completion_tokens": "\"1775000\"",
      "total_tokens": "\"294402000\"",
      "estimated_cost": 0.4096467,
      "prompt_cache_creation_tokens": 85528,
      "prompt_cached_tokens": 207039
    },
    "metadata": {
      "description": "Check if the output is factually correct",
      "difficulty": "easy"
    }
  },
  {
    "input": "factuality",
    "output": {
      "baseline": {
        "NumericDiff": {
          "avg": 0.683,
          "max": 1,
          "min": 0
        }
      },
      "metrics": {
        "completed": 11,
        "errors": 0,
        "invalidToolCalls": 0,
        "toolCalls": 11,
        "toolCallsByType": {
          "continue_execution": 1,
          "edit_task": 3,
          "get_available_scorers": 1,
          "get_playground_summary": 1,
          "get_results": 3,
          "run_task": 2
        }
      }
    },
    "error": null,
    "expected": null,
    "tags": null,
    "scores": {
      "avg_improvement_per_edit": 0.06266666666666665,
      "no_invalid_tool_calls": 1,
      "score_improvement": 0.18799999999999992,
      "tool_success_rate": 1
    },
    "metrics": {
      "prompt_tokens": "\"314370000\"",
      "completion_tokens": "\"2291000\"",
      "total_tokens": "\"316661000\"",
      "estimated_cost": 0.39029864999999997,
      "prompt_cache_creation_tokens": 75781,
      "prompt_cached_tokens": 238523
    },
    "metadata": {
      "description": "Check if the output is factually correct",
      "difficulty": "easy"
    }
  }
]

Task 2: Unsupervised improvement claude-sonnet-3-7
[
  {
    "input": "factuality",
    "output": {
      "baseline": {
        "NumericDiff": {
          "avg": 0.725,
          "max": 1,
          "min": 0
        }
      },
      "metrics": {
        "completed": 9,
        "errors": 1,
        "invalidToolCalls": 1,
        "toolCalls": 10,
        "toolCallsByType": {
          "edit_task": 4,
          "get_playground_summary": 1,
          "get_results": 3,
          "run_task": 2
        }
      },
      "updated": {
        "NumericDiff": {
          "avg": 0.667,
          "max": 1,
          "min": 0
        }
      }
    },
    "error": null,
    "expected": null,
    "tags": null,
    "scores": {
      "avg_improvement_per_edit": 0,
      "no_invalid_tool_calls": 0,
      "score_improvement": 0,
      "tool_success_rate": 0.9
    },
    "metrics": {
      "prompt_tokens": "\"202779000\"",
      "completion_tokens": "\"1310000\"",
      "total_tokens": "\"204089000\"",
      "estimated_cost": 0.28785855000000005,
      "prompt_cache_creation_tokens": 62706,
      "prompt_cached_tokens": 140011
    },
    "metadata": {
      "description": "Figure out movie titles based on a description",
      "difficulty": "easy"
    },
    "id": "e1def951-1706-4425-ab02-d5ef10e09bbc",
    "span_id": "75c7607c-6f11-4719-a8fc-f1ea8a7fe912",
    "origin": null
  },
  {
    "input": "factuality",
    "output": {
      "baseline": {
        "NumericDiff": {
          "avg": 0.742,
          "max": 1,
          "min": 0
        }
      },
      "metrics": {
        "completed": 10,
        "errors": 1,
        "invalidToolCalls": 0,
        "toolCalls": 11,
        "toolCallsByType": {
          "continue_execution": 1,
          "edit_task": 4,
          "get_playground_summary": 1,
          "get_results": 3,
          "run_task": 2
        }
      },
      "updated": {
        "NumericDiff": {
          "avg": 0.095,
          "max": 1,
          "min": 0
        }
      }
    },
    "error": null,
    "expected": null,
    "tags": null,
    "scores": {
      "avg_improvement_per_edit": 0,
      "no_invalid_tool_calls": 1,
      "score_improvement": 0,
      "tool_success_rate": 0.9090909090909092
    },
    "metrics": {
      "prompt_tokens": "\"202616000\"",
      "completion_tokens": "\"1603000\"",
      "total_tokens": "\"204219000\"",
      "estimated_cost": 0.2454798,
      "prompt_cache_creation_tokens": 46512,
      "prompt_cached_tokens": 156036
    },
    "metadata": {
      "description": "Figure out movie titles based on a description",
      "difficulty": "easy"
    },
    "id": "149f676b-e3d7-49ec-84fa-5176e172e84d",
    "span_id": "aad17419-81fa-476e-81d9-510180e9430a",
    "origin": null
  },
  {
    "input": "ai-search",
    "output": {
      "baseline": {
        "BTQL query checker": {
          "avg": 0.042,
          "max": 0.25,
          "min": 0
        },
        "QuerySimilarity": {
          "avg": 0.454,
          "max": 0.759,
          "min": 0
        }
      },
      "metrics": {
        "completed": 10,
        "errors": 0,
        "invalidToolCalls": 1,
        "toolCalls": 10,
        "toolCallsByType": {
          "edit_task": 3,
          "get_playground_summary": 1,
          "get_results": 3,
          "run_task": 3
        }
      },
      "updated": {
        "BTQL query checker": {
          "avg": 0.125,
          "max": 1,
          "min": 0
        },
        "QuerySimilarity": {
          "avg": 0.487,
          "max": 0.759,
          "min": 0
        }
      }
    },
    "error": null,
    "expected": null,
    "tags": null,
    "scores": {
      "avg_improvement_per_edit": 0.038666666666666655,
      "no_invalid_tool_calls": 0,
      "score_improvement": 0.05799999999999998,
      "tool_success_rate": 1
    },
    "metrics": {
      "prompt_tokens": "\"65782000\"",
      "completion_tokens": "\"2240000\"",
      "total_tokens": "\"68022000\"",
      "estimated_cost": 0.10710465,
      "prompt_cache_creation_tokens": 15537,
      "prompt_cached_tokens": 50183
    },
    "metadata": {
      "description": "Evaluate the performance of an AI search engine",
      "difficulty": "medium"
    },
    "id": "ab2ff3d5-4ccc-496b-bd98-0594faff2fbe",
    "span_id": "6b80dc71-930a-423b-a27a-98526085b526",
    "origin": null
  },
  {
    "input": "factuality",
    "output": {
      "baseline": {
        "NumericDiff": {
          "avg": 0.725,
          "max": 1,
          "min": 0
        }
      },
      "metrics": {
        "completed": 10,
        "errors": 0,
        "invalidToolCalls": 0,
        "toolCalls": 10,
        "toolCallsByType": {
          "edit_task": 3,
          "get_playground_summary": 1,
          "get_results": 3,
          "run_task": 3
        }
      },
      "updated": {
        "NumericDiff": {
          "avg": 0.846,
          "max": 1,
          "min": 0
        }
      }
    },
    "error": null,
    "expected": null,
    "tags": null,
    "scores": {
      "avg_improvement_per_edit": 0.04033333333333333,
      "no_invalid_tool_calls": 1,
      "score_improvement": 0.121,
      "tool_success_rate": 1
    },
    "metrics": {
      "prompt_tokens": "\"188993000\"",
      "completion_tokens": "\"2026000\"",
      "total_tokens": "\"191019000\"",
      "estimated_cost": 0.2667972,
      "prompt_cache_creation_tokens": 54687,
      "prompt_cached_tokens": 134244
    },
    "metadata": {
      "description": "Figure out movie titles based on a description",
      "difficulty": "easy"
    },
    "id": "12702fe2-6c3c-41cd-87bf-afe0862ddb7c",
    "span_id": "9e0592b1-84e4-451f-bf11-041b1338bed9",
    "origin": null
  },
  {
    "input": "factuality",
    "output": {
      "baseline": {
        "NumericDiff": {
          "avg": 0.7,
          "max": 1,
          "min": 0
        }
      },
      "metrics": {
        "completed": 11,
        "errors": 0,
        "invalidToolCalls": 0,
        "toolCalls": 11,
        "toolCallsByType": {
          "continue_execution": 1,
          "edit_task": 3,
          "get_playground_summary": 1,
          "get_results": 3,
          "run_task": 3
        }
      },
      "updated": {
        "NumericDiff": {
          "avg": 0.846,
          "max": 1,
          "min": 0
        }
      }
    },
    "error": null,
    "expected": null,
    "tags": null,
    "scores": {
      "avg_improvement_per_edit": 0.04866666666666667,
      "no_invalid_tool_calls": 1,
      "score_improvement": 0.14600000000000002,
      "tool_success_rate": 1
    },
    "metrics": {
      "prompt_tokens": "\"221550000\"",
      "completion_tokens": "\"1981000\"",
      "total_tokens": "\"223531000\"",
      "estimated_cost": 0.29506635,
      "prompt_cache_creation_tokens": 57595,
      "prompt_cached_tokens": 163887
    },
    "metadata": {
      "description": "Figure out movie titles based on a description",
      "difficulty": "easy"
    }
  }
]
output:
The following experiment data show that from unsupervised improvement claude-sonnet-4 showed significant score improvement over claude-sonnet-3-7 on no_invalid_tool_calls.
The improvements are as follows:
- avg_improvement_per_edit went from 28.21 to 28.71.
- score_improvement went from 51.44 to 54.85.
- tool success rate went from 99.05 to 99.5.

Note worthy metrics are as follows:
- LLM cost went up from 3.04 to 3.72.

The score improvement to go from 55% to 90% on no_invalid_tool_calls is very significant.
What is interesting to note is that despite the no_invalid_tool_calls scorer improving immensely, the rest of the scores showed not the strongest improvement.


Evaluation 1: a


For checking factual correctness, look for {{{expected}}} in the {{{output}}}.

Compare each of them, and categorize them as the following:

(a) The result does a good job of recounting the factual results of the experiment and the comparison between the experiments if there is more than one, as well as providing a summarizing conclusion that highlights any insightful findings.
(b) The result offers one of factual results or insightful finginds, but not both.
(c) The result neither offers factual results nor fails to recount factual results.
`,
  choiceScores: {
    a: 1,
    b: 0.5,
    c: 0,
  },
});

// Scoring function to evaluate summary quality
export function scoreSummaryCompleteness({
  output,
}: {
  output: {
    summary: string;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
    metrics: any;
    experimentData: {
      taskName: string;
      // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
      scores: Record<string, any>;
    };
  };
}): Score {
  const summary = output.summary;

  // Check for key elements that should be in a good experiment summary
  const hasTaskName = summary.includes(output.experimentData.taskName);
  const hasScoreInfo = Object.keys(output.experimentData.scores).some(
    (scoreKey) => summary.toLowerCase().includes(scoreKey.toLowerCase()),
  );

  // Check for general analysis keywords
  const hasAnalysisKeywords = [
    "performance",
    "result",
    "score",
    "accuracy",
    "quality",
    "evaluation",
  ].some((keyword) => summary.toLowerCase().includes(keyword));

  // Simple completeness score (0-1)
  const completenessScore =
    ((hasTaskName ? 1 : 0) +
      (hasScoreInfo ? 1 : 0) +
      (hasAnalysisKeywords ? 1 : 0)) /
    4;

  return {
    name: "summary_completeness",
    score: completenessScore,
    metadata: {
      hasTaskName,
      hasScoreInfo,
      hasAnalysisKeywords,
      summaryLength: summary.length,
    },
  };
}

// Scoring function to evaluate summary conciseness
export function scoreSummaryConciseness({
  output,
}: {
  output: {
    summary: string;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
    metrics: any;
    experimentData: {
      taskName: string;
      // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
      scores: Record<string, any>;
    };
  };
}): Score {
  const summary = output.summary;
  const wordCount = summary.split(/\s+/).length;

  // Good summaries should be informative but concise
  // Penalize summaries that are too short (<50 words) or too long (>500 words)
  let score = 1;
  if (wordCount < 50) {
    score = Math.max(0, wordCount / 50);
  } else if (wordCount > 500) {
    score = Math.max(0, 1 - (wordCount - 500) / 500);
  }

  return {
    name: "summary_conciseness",
    score: score,
    metadata: {
      wordCount,
      characterCount: summary.length,
    },
  };
}

// Scoring function to evaluate tool usage efficiency
export function scoreToolUsage({
  output,
}: {
  output: {
    summary: string;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
    metrics: any;
    experimentData: {
      taskName: string;
      // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
      scores: Record<string, any>;
    };
  };
}): Score {
  const metrics = output.metrics;
  const toolCallsByType = metrics.toolCallsByType || {};

  const hasInferSchema = (toolCallsByType.infer_schema || 0) > 0;
  const hasBtqlQuery = (toolCallsByType.btql_query || 0) > 0;

  // Calculate base score based on required tools
  let score = 0;
  if (hasInferSchema && hasBtqlQuery) {
    score = 1; // Perfect score if both required tools are used
  } else if (hasInferSchema || hasBtqlQuery) {
    score = 0.5; // Partial score if only one required tool is used
  } else {
    score = 0; // No score if neither required tool is used
  }

  return {
    name: "correct_tool_usage",
    score: score,
    metadata: {
      toolCalls: metrics.toolCalls,
      toolCallsByType: metrics.toolCallsByType,
      hasInferSchema,
      hasBtqlQuery,
      requiredToolsUsed: hasInferSchema && hasBtqlQuery,
    },
  };
}
