name: Deploy to Cloudflare

on:
  workflow_dispatch:
  push:
    branches: ["main"]

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v5
      - uses: pnpm/action-setup@v4
      - uses: actions/setup-node@v5
        with:
          node-version: 22
          cache: "pnpm"
      - run: pnpm install
      - run: pnpm run build --filter=@braintrust/ai-proxy-wrangler
      - name: Configure wrangler.toml
        working-directory: apis/cloudflare
        run: |
          cp wrangler-template.toml wrangler.toml
          sed -i 's/<YOUR_KV_ID>/${{ secrets.CLOUDFLARE_KV_ID }}/g' wrangler.toml
          sed -i 's/<YOUR_PASSWORD>/${{ secrets.PROMETHEUS_SCRAPE_PASSWORD }}/g' wrangler.toml
      - run: pnpm run deploy
        working-directory: apis/cloudflare
        env:
          CLOUDFLARE_API_TOKEN: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          CLOUDFLARE_ACCOUNT_ID: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
