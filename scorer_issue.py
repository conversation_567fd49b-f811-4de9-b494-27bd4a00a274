import traceback
from pydantic import BaseModel
try:
    from autoevals import Score
except ImportError as e:
    raise ValueError(traceback.format_exc())

def test_scorer(input: dict, output: dict) -> Score:
    return Score(
        name="Test scorer always 1.0",
        score=1.0,
    )
class Input(BaseModel):
    input: dict
    output: dict

import braintrust
project = braintrust.projects.create("AIA2 sandbox")
project.scorers.create(
    name="test scorer",
    description="Test",
    parameters=Input,
    handler=test_scorer,
)