import {
  // EmptyStateCodeSnippets,
  ORGS_WITH_DISABLED_EMPTY_STATE_CODE_SNIPPETS,
  TableEmptyState,
} from "#/ui/table/TableEmptyState";
import { Beaker, BeakerIcon } from "lucide-react";
import {
  CreateExperimentForm,
  type createExperimentFormSchema,
  CreateSingleExperimentNullStateDialog,
} from "../../../prompt/[prompt]/create-experiment-dialog";
import { Button } from "#/ui/button";
import { useOrg } from "#/utils/user";
import { useContext, useMemo, useState } from "react";
import { ProjectContext } from "../projectContext";
import { useScorerFunctions } from "../../../prompt/[prompt]/scorers/open";
import { CreateExperimentSdkSnippet } from "../../../prompt/[prompt]/create-experiment-sdk-snippet";
import { type z } from "zod";
import Link from "next/link";

export const ExperimentsEmptyState = () => {
  const org = useOrg();
  const orgName = org.name;
  const { projectName, orgDatasets } = useContext(ProjectContext);

  const { functions } = useScorerFunctions({});

  const [formValues, setFormValues] = useState<z.infer<
    typeof createExperimentFormSchema
  > | null>(null);

  const selectedDataset = useMemo(() => {
    if (!formValues?.dataset || !orgDatasets) return null;
    const dataset = orgDatasets.find((d) => d.id === formValues.dataset);
    return { datasetName: dataset?.name, projectName: dataset?.project_name };
  }, [formValues?.dataset, orgDatasets]);

  if (ORGS_WITH_DISABLED_EMPTY_STATE_CODE_SNIPPETS.includes(orgName)) {
    return (
      <TableEmptyState
        className="mt-3"
        Icon={Beaker}
        labelClassName="text-sm leading-normal"
        label={
          <>
            <span className="mb-3 block text-base">
              Get started with experiments
            </span>
            Experiments are snapshots of your AI application at a point in time.
            They are used to evaluate and compare performance over time.
            <div className="mt-3 mb-8">
              <CreateSingleExperimentNullStateDialog>
                <Button>Create experiment</Button>
              </CreateSingleExperimentNullStateDialog>
            </div>
          </>
        }
      />
    );
  }

  return (
    <div className="mt-3 rounded-md border border-primary-100 bg-primary-50 p-4 xl:p-8">
      <div className="mx-auto mb-12 flex max-w-lg flex-col text-base text-pretty text-primary-500 xl:items-center xl:text-center">
        <BeakerIcon className="mb-6 size-6" />
        <p className="text-sm">
          <span className="mb-3 block text-base">
            Get started with experiments
          </span>
          Experiments are snapshots of your AI application at a point in time.
          They are used to evaluate and compare performance over time.{" "}
          <Link
            className="inline-block text-accent-600"
            href="/docs/guides/experiments"
            target="_blank"
          >
            Learn more
          </Link>{" "}
          about experiments.
        </p>
      </div>
      <div className="flex flex-col items-center gap-2 xl:max-w-none xl:flex-row xl:items-stretch xl:justify-center">
        <div className="w-full max-w-lg rounded-lg border border-primary-100 bg-background p-6">
          <div className="mb-4 w-full text-base font-semibold text-primary-800">
            Create experiments
          </div>
          <CreateExperimentForm
            mode="single"
            tasks={[]}
            scorers={[]}
            scorerFunctions={functions}
            maxConcurrency={10}
            strict={false}
            onFormChange={setFormValues}
            footerClassName="sm:justify-start"
          />
        </div>
        <div className="max-w-lg flex-1 rounded-lg border border-primary-100 bg-background p-6">
          <div className="mb-4 text-base font-semibold text-primary-800">
            Or use the Braintrust SDK to run experiments
          </div>
          <CreateExperimentSdkSnippet
            tasks={formValues?.tasks ?? []}
            dataset={selectedDataset}
            scorers={formValues?.scorers ?? []}
            scorerFunctions={functions}
            projectName={projectName}
            metadata={formValues?.metadata}
            maxConcurrency={formValues?.maxConcurrency}
            trialCount={formValues?.trialCount}
            strict={formValues?.strict}
          />
        </div>
      </div>
    </div>
  );
};
