"use client";
import { <PERSON><PERSON> } from "#/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "#/ui/dialog";
import { useContext, useState, useEffect, useCallback } from "react";
import type { OnlineScoringTestResults } from "@braintrust/local/functions";
import { ProjectContext } from "#/app/app/[org]/p/[project]/projectContext";
import { toast } from "sonner";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { ScorersDropdownWithCreateDialog } from "#/app/app/[org]/prompt/[prompt]/scorers/scorers-dropdown";
import { type SavedScorer } from "#/utils/scorers";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "#/ui/form";
import { InfoBanner } from "#/ui/info-banner";
import { useOrg } from "#/utils/user";
import { type DataObjectSearch } from "#/utils/btapi/btapi";
import { useSessionToken } from "#/utils/auth/session-token";
import {
  type ApplyScorersResponse,
  applyScorersToLogs,
  MAX_LOGS_SCORING_LIMIT,
} from "#/utils/btapi/scoring";

// Extract key error information for grouping similar errors
const extractErrorKey = (error: string): string => {
  // Try to extract and parse JSON from the error string
  const jsonMatch = error.match(/\{.*\}/);
  if (jsonMatch) {
    try {
      const parsed = JSON.parse(jsonMatch[0]);
      if (parsed.error?.message) {
        return parsed.error.message.replace(/^messages:\s*/, "");
      }
    } catch {
      // JSON parsing failed, continue with pattern matching
    }
  }

  // Common patterns to normalize
  const patterns = [
    { match: /JWT is expired/, key: "JWT is expired" },
    { match: /invalid x-api-key/, key: "Invalid API key" },
    {
      match: /Failed to get function metadata/,
      key: "Failed to get function metadata",
    },
    { match: /Function not found/, key: "Function not found" },
    { match: /timeout|Timeout/i, key: "Timeout error" },
    { match: /ECONNREFUSED|ENOTFOUND/, key: "Network connection error" },
    {
      match: /AI provider returned (\d+) error: (.+?)(?:\s+Headers:|$)/,
      key: (match: RegExpMatchArray) => {
        const statusCode = match[1];
        const errorBody = match[2];
        const jsonMatch = errorBody.match(/"message":"([^"]+)"/);
        return jsonMatch
          ? `AI provider ${statusCode}: ${jsonMatch[1]}`
          : `AI provider ${statusCode}: ${errorBody}`;
      },
    },
  ];

  for (const pattern of patterns) {
    const match = error.match(pattern.match);
    if (match) {
      return typeof pattern.key === "function"
        ? pattern.key(match)
        : pattern.key;
    }
  }

  // Fallback: extract first meaningful line
  const firstLine = error.split("\n").find((line) => line.trim()) || error;
  return firstLine
    .replace(/^(Error|TypeError|ReferenceError|SyntaxError): /, "")
    .substring(0, 100);
};

const scoringSchema = z.object({
  scorers: z.array(
    z.union([
      z.object({ type: z.literal("function"), id: z.string() }),
      z.object({ type: z.literal("global"), name: z.string() }),
    ]),
  ),
});

type ScoringFormData = z.infer<typeof scoringSchema>;

// Use existing types from the async scoring system
type RowResult = OnlineScoringTestResults[number];
function isRowResult(obj: unknown): obj is RowResult {
  return typeof obj === "object" && obj !== null && "results" in obj;
}

type BaseProps = {
  onClose: VoidFunction;
  preSelectedScorerId?: string;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  title: string;
  description: string;
};

type AsyncScoringDialogProps = BaseProps &
  (
    | {
        mode: "filtered";
        filters: DataObjectSearch["filters"];
        infoBannerContent: string;
      }
    | {
        mode: "selected";
        selectedLogIds: string[];
        selectedLogsCount: number;
        infoBannerContent?: string;
      }
  );

export function AsyncScoringDialog(props: AsyncScoringDialogProps) {
  const {
    open,
    onOpenChange,
    onClose,
    preSelectedScorerId,
    title,
    description,
  } = props;
  const { projectId, projectName } = useContext(ProjectContext);
  const org = useOrg();
  const { getOrRefreshToken } = useSessionToken();
  const [isCreatingScorer, setIsCreatingScorer] = useState(false);
  const [isRunningScorer, setIsRunningScorer] = useState(false);

  const applyScorerWithFilter = useCallback(
    async (
      scorers: SavedScorer[],
      btqlFilter: string,
    ): Promise<ApplyScorersResponse> => {
      const sessionToken = await getOrRefreshToken();
      return await applyScorersToLogs({
        apiUrl: org.api_url,
        sessionToken,
        proxyUrl: org.api_url,
        orgName: org.name,
        projectId: projectId!,
        scorers,
        btqlFilter,
      });
    },
    [getOrRefreshToken, org.api_url, org.name, projectId],
  );

  const applyScorerWithLogIds = useCallback(
    async (
      scorers: SavedScorer[],
      logIds: string[],
    ): Promise<ApplyScorersResponse> => {
      const sessionToken = await getOrRefreshToken();
      return await applyScorersToLogs({
        apiUrl: org.api_url,
        sessionToken,
        proxyUrl: org.api_url,
        orgName: org.name,
        projectId: projectId!,
        scorers,
        logIds,
      });
    },
    [getOrRefreshToken, org.api_url, org.name, projectId],
  );

  const form = useForm<ScoringFormData>({
    resolver: zodResolver(scoringSchema),
    defaultValues: {
      scorers: preSelectedScorerId
        ? [{ type: "function" as const, id: preSelectedScorerId }]
        : [],
    },
  });

  const scorersCount = form.watch("scorers").length;
  const hasScorers = scorersCount > 0;

  // Reset loading state when dialog opens/closes
  useEffect(() => {
    if (!open) {
      setIsRunningScorer(false);
      form.setValue("scorers", [], { shouldDirty: false });
    }
  }, [open, form]);

  const consolidateErrors = useCallback((results: unknown[]) => {
    const uniqueErrors = new Map<
      string,
      { scorer: string; error: string; count: number }
    >();

    results.forEach((rowResult) => {
      if (isRowResult(rowResult)) {
        rowResult.results?.forEach((scorerResult) => {
          if (scorerResult.kind === "error" && scorerResult.error) {
            const scorer =
              "global_function" in scorerResult
                ? scorerResult.global_function
                : "function_id" in scorerResult
                  ? scorerResult.function_id
                  : "Unknown";
            const error = scorerResult.error;
            const errorKey = extractErrorKey(error);
            const key = `${scorer}:${errorKey}`;

            const existing = uniqueErrors.get(key);
            uniqueErrors.set(key, {
              scorer,
              error,
              count: (existing?.count || 0) + 1,
            });
          }
        });
      }
    });

    return Array.from(uniqueErrors.values());
  }, []);

  const applyScorers = useCallback(
    async (data: ScoringFormData) => {
      // Close dialog immediately when scoring starts
      onClose();

      const scoringPromise = async () => {
        const result =
          props.mode === "filtered"
            ? await applyScorerWithFilter(
                data.scorers,
                props.filters?.btql
                  ?.map((expr) =>
                    typeof expr === "string"
                      ? expr
                      : "btql" in expr
                        ? expr.btql
                        : "",
                  )
                  .filter(Boolean)
                  .join(" AND ") || "",
              )
            : await applyScorerWithLogIds(
                data.scorers,
                props.selectedLogIds.slice(0, MAX_LOGS_SCORING_LIMIT),
              );

        if (result.kind === "error") {
          throw new Error(result.message);
        }

        const {
          processed_count = 0,
          success_count = 0,
          error_count = 0,
          results = [],
        } = result.payload || {};

        if (error_count > 0) {
          // Consolidate unique errors for the error message
          const uniqueErrors = consolidateErrors(results);
          const errorSummary = uniqueErrors
            .slice(0, 3)
            .map((err) => {
              const errorKey = extractErrorKey(err.error);
              return errorKey[0]?.toUpperCase() + errorKey.slice(1);
            })
            .join("; ");
          const additionalErrors =
            uniqueErrors.length > 3
              ? ` and ${uniqueErrors.length - 3} more`
              : "";

          throw new Error(
            `Applied to ${processed_count} log${processed_count !== 1 ? "s" : ""}: ${success_count} successful, ${error_count} failed. Error${uniqueErrors.length !== 1 ? "s" : ""}: ${errorSummary}${additionalErrors}`,
          );
        }

        return {
          processed_count,
          success_count,
          scorersCount,
        };
      };

      // Use toast.promise to handle loading, success, and error states
      toast.promise(scoringPromise(), {
        loading: `Applying ${scorersCount} scorer${scorersCount !== 1 ? "s" : ""}...`,
        success: (data) =>
          `Successfully applied ${data.scorersCount} scorer${data.scorersCount !== 1 ? "s" : ""} to ${data.processed_count} log${data.processed_count !== 1 ? "s" : ""}`,
        error: (error) =>
          error instanceof Error ? error.message : "Failed to apply scorers",
      });
    },
    [
      applyScorerWithFilter,
      applyScorerWithLogIds,
      consolidateErrors,
      scorersCount,
      props,
      onClose,
    ],
  );

  if (!projectId) {
    return null;
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="flex flex-col p-0">
        <Form {...form}>
          <div className="flex flex-1 flex-col overflow-hidden">
            <form
              onSubmit={form.handleSubmit(applyScorers)}
              className="flex flex-1 flex-col overflow-hidden"
            >
              <div className="flex flex-1 flex-col gap-6 overflow-y-auto p-4">
                <DialogHeader className="flex-none">
                  <DialogTitle>{title}</DialogTitle>
                  <DialogDescription>{description}</DialogDescription>
                </DialogHeader>
                <div>
                  <FormField
                    control={form.control}
                    name="scorers"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="flex justify-between">
                          Scorers
                          {hasScorers && (
                            <Button
                              size="inline"
                              transparent
                              className="text-xs text-primary-500"
                              onClick={(e) => {
                                e.preventDefault();
                                form.setValue("scorers", [], {
                                  shouldDirty: true,
                                });
                              }}
                            >
                              Clear
                            </Button>
                          )}
                        </FormLabel>
                        <FormControl>
                          {projectId && (
                            <ScorersDropdownWithCreateDialog
                              projectId={projectId}
                              projectName={projectName}
                              savedScorers={field.value}
                              updateScorers={async (scorers) => {
                                field.onChange(scorers);
                                return Promise.resolve(null);
                              }}
                              disableScorersThatRequireConfiguration
                              isCreatingScorer={isCreatingScorer}
                              setIsCreatingScorer={setIsCreatingScorer}
                              hideSelected={false}
                            />
                          )}
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {scorersCount > 0 && props.infoBannerContent && (
                    <InfoBanner>{props.infoBannerContent}</InfoBanner>
                  )}
                </div>
              </div>

              <DialogFooter className="flex-none border-t px-4 py-3">
                <div className="flex w-full items-center justify-end gap-2">
                  <Button variant="ghost" onClick={onClose}>
                    Cancel
                  </Button>
                  <Button
                    variant="primary"
                    type="submit"
                    disabled={!hasScorers}
                    isLoading={isRunningScorer}
                  >
                    Apply scorers
                  </Button>
                </div>
              </DialogFooter>
            </form>
          </div>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
