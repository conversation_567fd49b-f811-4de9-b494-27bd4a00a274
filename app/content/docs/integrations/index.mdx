---
title: "Integrations"
description: "Integrating with existing frameworks"
---

import { IntegrationCard } from "../card";
import { OpenTelemetryLogo, VercelLogo, MastraLogo, Langchain<PERSON>ogo, LanggraphLogo, PydanticLogo, AutogenLogo, CrewAILogo, Cloudflare<PERSON>ogo, Strands<PERSON>ogo, <PERSON>ogo, LiveKit<PERSON>ogo } from "../../../app/app/[org]/onboarding-logos";
import { OpenAI, Anthropic, Meta, Gemini, Mistral, Together, Fireworks, Perplexity, XAI, Groq, Lepton, Cerebras, Ollama, Replicate, Baseten, Amazon, GoogleCloud, Azure, Databricks, Google } from "#/ui/icons/providers";
import { SquareDashedIcon } from "lucide-react";

# Integrations

Trace your apps using existing frameworks to quickly add observability. This guide walks you through the supported integrations and how to configure them for maximum observability and insight.

<div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-5">
  <IntegrationCard href="/docs/integrations/opentelemetry" title="OpenTelemetry">
    <OpenTelemetryLogo size={64} />
  </IntegrationCard>
  <IntegrationCard href="/docs/integrations/vercel-ai-sdk" title="Vercel AI SDK">
    <VercelLogo size={64} />
  </IntegrationCard>
  <IntegrationCard href="/docs/integrations/openai-agents-sdk" title="Agents SDK">
    <OpenAI size={64} />
  </IntegrationCard>
  <IntegrationCard href="/docs/integrations/claude-agent-sdk" title="Claude Agent SDK">
    <ClaudeLogo size={64} />
  </IntegrationCard>
  <IntegrationCard href="/docs/integrations/instructor" title="Instructor">
    <SquareDashedIcon size={64} className="text-primary-400" />
  </IntegrationCard>
  <IntegrationCard href="/docs/integrations/langchain" title="LangChain">
    <LangchainLogo size={64} />
  </IntegrationCard>
  <IntegrationCard href="/docs/integrations/langgraph" title="LangGraph">
    <LanggraphLogo size={64} />
  </IntegrationCard>
  <IntegrationCard href="/docs/integrations/google" title="Google ADK">
    <Google size={64} />
  </IntegrationCard>
  <IntegrationCard href="/docs/integrations/mastra" title="Mastra">
    <MastraLogo size={64} />
  </IntegrationCard>
   <IntegrationCard href="/docs/integrations/pydantic-ai" title="Pydantic AI">
    <PydanticLogo size={64} />
  </IntegrationCard>
  <IntegrationCard href="/docs/integrations/autogen" title="Autogen">
    <AutogenLogo size={64} />
  </IntegrationCard>
  <IntegrationCard href="/docs/integrations/crew-ai" title="CrewAI">
    <CrewAILogo size={64} />
  </IntegrationCard>
    <IntegrationCard href="/docs/integrations/strands-agent" title="Strands Agent SDK">
    <StrandsLogo size={64} />
  </IntegrationCard>
  <IntegrationCard href="/docs/integrations/cloudflare" title="Cloudflare">
    <CloudflareLogo size={64} />
  </IntegrationCard>
  <IntegrationCard href="/docs/integrations/livekit-agents" title="LiveKit Agents">
    <LiveKitLogo size={64} />
  </IntegrationCard>
</div>
