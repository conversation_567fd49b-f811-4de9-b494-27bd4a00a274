---
title: "LiveKit Agents"
---

import ModuleInstaller from "#/ui/docs/module-installer";

# LiveKit Agents

Braintrust traces [LiveKit Agents](https://livekit.io/) applications via OpenTelemetry.

This integration uses Braintrust's [Python SDK configuration](https://www.braintrust.dev/docs/integrations/opentelemetry#python-sdk-configuration) for OpenTelemetry.

<ModuleInstaller languages={["py"]} packageNames="braintrust[otel] livekit-agents livekit-plugins-openai opentelemetry-sdk" />

To trace LiveKit agents with Braintrust, configure these environment variables:

```bash title=".env"
BRAINTRUST_API_KEY=your-api-key
BRAINTRUST_PARENT=project_name:livekit-demo
OPENAI_API_KEY=your-openai-api-key
```

When you create your agent, enable telemetry and export the data using OpenTelemetry:

```python title="livekit_agent.py"
from braintrust.otel import BraintrustSpanProcessor
from livekit import agents
from livekit.agents import Agent, AgentSession, RoomInputOptions
from livekit.agents.telemetry import set_tracer_provider
from livekit.plugins import noise_cancellation, openai
from opentelemetry.sdk.trace import TracerProvider


def setup_braintrust_telemetry():
    """Setup Braintrust OTEL telemetry for agent monitoring"""
    trace_provider = TracerProvider()
    trace_provider.add_span_processor(BraintrustSpanProcessor())
    set_tracer_provider(trace_provider)


class Assistant(Agent):
    def __init__(self) -> None:
        super().__init__(instructions="You are a helpful voice AI assistant.")


async def entrypoint(ctx: agents.JobContext):
    # Setup telemetry
    setup_braintrust_telemetry()

    # Create agent session with OpenAI realtime model
    session = AgentSession(llm=openai.realtime.RealtimeModel(voice="coral"))

    # Start session with assistant agent
    await session.start(
        room=ctx.room,
        agent=Assistant(),
        room_input_options=RoomInputOptions(
            noise_cancellation=noise_cancellation.BVC(),
        ),
    )


# Run script locally with `python livekit_agent.py console`
if __name__ == "__main__":
    agents.cli.run_app(agents.WorkerOptions(entrypoint_fnc=entrypoint))
```
