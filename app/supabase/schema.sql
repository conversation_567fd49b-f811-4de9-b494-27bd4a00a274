set statement_timeout = 0;
set lock_timeout = 0;
set idle_in_transaction_session_timeout = 0;
set client_encoding = 'UTF8';
set standard_conforming_strings = on;
select pg_catalog.set_config('search_path', '', false);
set xmloption = content;
set client_min_messages = warning;
set row_security = off;

-- Configure extensions

create schema "extensions";
create schema "pgsodium";
create schema "graphql";
create schema "vault";
create schema "auth";

create extension "pg_net" with schema "extensions";
create extension "pgsodium" with schema "pgsodium";
create extension "pg_graphql" with schema "graphql";
create extension "pg_stat_statements" with schema "extensions";
create extension "pgcrypto" with schema "extensions";
create extension "pgjwt" with schema "extensions";
create extension "supabase_vault" with schema "vault";
create extension "uuid-ossp" with schema "extensions";

-- Default permissions settings

grant usage on schema "public" to "postgres";
grant usage on schema "public" to "anon";
grant usage on schema "public" to "authenticated";
grant usage on schema "public" to "service_role";

alter default privileges for role postgres in schema public grant all on sequences to postgres;
alter default privileges for role postgres in schema public grant all on sequences to anon;
alter default privileges for role postgres in schema public grant all on sequences to authenticated;
alter default privileges for role postgres in schema public grant all on sequences to service_role;

alter default privileges for role postgres in schema public grant all on functions to postgres;
alter default privileges for role postgres in schema public grant all on functions to anon;
alter default privileges for role postgres in schema public grant all on functions to authenticated;
alter default privileges for role postgres in schema public grant all on functions to service_role;

alter default privileges for role postgres in schema public grant all on tables to postgres;
alter default privileges for role postgres in schema public grant all on tables to anon;
alter default privileges for role postgres in schema public grant all on tables to authenticated;
alter default privileges for role postgres in schema public grant all on tables to service_role;

alter default privileges revoke all on functions from public;
alter default privileges for role postgres in schema public revoke all on functions from anon;
grant execute on all functions in schema pgsodium to service_role;

set search_path = public, extensions;

-- BEGIN CONSTANTS FOR ANYNOMOUS USER

create function anon_user_id()
returns uuid language sql immutable parallel safe
as $$
    SELECT '80c48a10-4888-4382-a55b-255018e70fe5'::uuid;
$$;

revoke execute on function anon_user_id from public, anon;

create function anon_user_auth_id()
returns uuid language sql immutable parallel safe
as $$
    SELECT '35b34362-e0bd-4b78-826f-343d9409b4f4'::uuid;
$$;

revoke execute on function anon_user_auth_id from public, anon;

create function anon_user_email()
returns text language sql immutable parallel safe
as $$
    SELECT '<EMAIL>';
$$;

revoke execute on function anon_user_email from public, anon;

-- END CONSTANTS FOR ANYNOMOUS USER

-- Utility types.

-- We support listing out the contents of certain tables in descending order of
-- creation time, while paginating by id. Thus we must index on the tuple of
-- both fields.
create type _created_and_id as (
    created timestamp with time zone,
    id uuid
);

-- Utility functions.

-- From
-- https://stackoverflow.com/questions/42944888/merging-jsonb-values-in-postgresql.
create or replace function jsonb_recursive_merge(a jsonb, b jsonb)
returns jsonb
language sql
as $$
    select
        jsonb_object_agg(
            coalesce(ka, kb),
            case
                when va isnull then vb
                when vb isnull then va
                when jsonb_typeof(va) <> 'object' or jsonb_typeof(vb) <> 'object' then vb
                else jsonb_recursive_merge(va, vb)
            end
        )
    from jsonb_each(a) e1(ka, va)
    full join jsonb_each(b) e2(kb, vb) on ka = kb
$$;

revoke execute on function jsonb_recursive_merge from anon;

-- From
-- https://stackoverflow.com/questions/********/postgres-select-where-the-where-is-uuid-or-string.
create or replace function uuid_or_null(str text)
returns uuid
language plpgsql immutable parallel safe
as $$
begin
  return str::uuid;
exception when invalid_text_representation then
  return null;
end;
$$;

revoke execute on function uuid_or_null from anon;

-- Define users and authorization functions.

create type user_type_enum as enum ('user', 'service_account');

create table users (
    id uuid not null primary key default uuid_generate_v4(),
    auth_id uuid unique default uuid_generate_v4(),
    given_name text,
    family_name text,
    email text unique,
    avatar_url text,
    created timestamp with time zone default current_timestamp,
    clerk_id text unique,
    user_type user_type_enum not null default 'user',
    internal_metadata jsonb
);

alter table users enable row level security;

create function get_user_email(user_id uuid)
returns text
language sql
stable
as $$
    select email
    from users
    where id = user_id;
$$;

revoke execute on function get_user_email from anon;

-- NOTE: we should stop using this function in new code. We see that queries run
-- much faster when they can provide the user_id directly (especially when this
-- UDF is called multiple times within the same query).
create function get_user_id_by_auth_id(auth_id uuid)
returns uuid
language sql
security definer
stable
as $$
SELECT id FROM users WHERE users.auth_id = get_user_id_by_auth_id.auth_id
$$;

revoke execute on function get_user_id_by_auth_id from public, anon;

-- Define organizations and membership of users in organizations.

create table organizations (
    id uuid not null primary key default uuid_generate_v4(),
    name text not null,
    api_url text,
    proxy_url text,
    realtime_url text,
    created timestamp with time zone default current_timestamp,
    is_universal_api boolean default null,
    internal_metadata jsonb
);

create unique index on organizations (name);

create table members (
    org_id uuid not null references organizations,
    user_id uuid not null references users,
    created timestamp with time zone default current_timestamp,
    primary key (org_id, user_id)
);

create index on members (user_id);

alter table organizations enable row level security;
alter table members enable row level security;

-- Api keys.

create table api_keys (
    id uuid not null primary key default uuid_generate_v4(),
    created timestamp with time zone default current_timestamp,
    key_hash bytea not null,
    name text not null,
    preview_name text not null,
    user_id uuid references users,
    org_id uuid references organizations,
    internal_metadata jsonb
);

-- Fast lookups
create index api_keys_user_id_key_hash on api_keys (user_id, key_hash);

alter table api_keys enable row level security;

create or replace function create_api_key_unchecked(
    auth_id uuid,
    org_id uuid,
    name text,
    is_service_token boolean)
returns jsonb
language plpgsql
security definer
as $$
DECLARE
  api_key text;
  prefix text;
  _created_api_key_id uuid;
BEGIN
  -- Remove + and / to make it easier to use in terminals, etc.
  api_key := replace(replace(encode(pgsodium.randombytes_buf(36), 'base64'), '+', '0'), '/', '1');
  IF NOT is_service_token THEN
    prefix := 'sk-';
  ELSE
    prefix := 'bt-st-';
  END IF;
  INSERT INTO api_keys (key_hash, name, preview_name, user_id, org_id)
  SELECT
    pgsodium.crypto_generichash(decode(api_key, 'base64')), name, CONCAT(prefix, RIGHT(api_key, 4)), id, org_id
    FROM users WHERE users.auth_id = create_api_key_unchecked.auth_id
  RETURNING id INTO _created_api_key_id;

  return (
    select jsonb_build_object(
        'api_key', (
          to_jsonb(api_keys) ||
          jsonb_build_object('key', concat(prefix, api_key)) ||
          jsonb_build_object('user_given_name', users.given_name) ||
          jsonb_build_object('user_family_name', users.family_name) ||
          jsonb_build_object('user_email', users.email)
        )
    )
    from api_keys
    join users on api_keys.user_id = users.id
    where api_keys.id = _created_api_key_id
  );
END;
$$;

revoke execute on function create_api_key_unchecked from anon;

create or replace function create_api_key_full(
    auth_id uuid,
    org_id uuid,
    name text,
    is_service_token boolean)
returns jsonb
language plpgsql
security definer
as $$
DECLARE
  _org_check int;
BEGIN
  SELECT 1
  FROM users JOIN members ON users.id = members.user_id
  WHERE users.auth_id = create_api_key_full.auth_id
    AND (members.org_id = create_api_key_full.org_id OR create_api_key_full.org_id IS NULL)
  INTO _org_check;

  if not found then
    raise exception '%', jsonb_build_object('kind', 'http-error', 'code', 400, 'message', format('User with auth_id %s does not belong to organization with id %s', auth_id, org_id));
  end if;

  return create_api_key_unchecked(auth_id, org_id, name, is_service_token);
END;
$$;

revoke execute on function create_api_key_full from anon;

-- We preserve the version of the function with a more restricted API for
-- existing callers.
create function create_api_key(
    auth_id uuid,
    org_id uuid,
    name text)
returns text
language sql
security definer
as $$ select create_api_key_full(auth_id, org_id, name, false)->'api_key'->>'key' $$;

revoke execute on function create_api_key from anon;

create function add_member_to_org_unchecked(
    user_to_add_id uuid,
    organization_id uuid,
    initial_group_ids uuid[])
returns uuid
language plpgsql
as $$
declare
    _added_user_id uuid;
    _initial_group_ids uuid[] := coalesce(initial_group_ids, '{}');
begin
    insert into members(org_id, user_id)
    values (organization_id, user_to_add_id)
    on conflict do nothing
    returning user_id into _added_user_id;

    insert into group_users(group_id, user_id)
    select group_id, user_to_add_id
    from unnest(_initial_group_ids) group_id
    where group_id is not null
    on conflict do nothing;

    return _added_user_id;
end;
$$;

revoke execute on function add_member_to_org_unchecked from anon;

create function remove_member_from_org_unchecked(
    user_to_remove_id uuid,
    organization_id uuid)
returns void
language plpgsql
as $$
begin
    -- We must first delete any instances of rows which are foreign-key linked
    -- to the (user_id, org_id) tuple in the members table.
    delete from acls where user_id = user_to_remove_id and _object_org_id = organization_id;
    delete from group_users where user_id = user_to_remove_id and _group_org_id = organization_id;
    delete from members where user_id = user_to_remove_id and org_id = organization_id;
end;
$$;

revoke execute on function remove_member_from_org_unchecked from anon;

create function remove_member_from_org(
    user_to_remove_id uuid,
    organization_id uuid,
    actor_auth_id uuid)
returns void
language plpgsql
as $$
declare
    actor_user_id uuid := get_user_id_by_auth_id(actor_auth_id);
begin
    if user_to_remove_id != actor_user_id and not has_under_organization_acl('org_member', organization_id, organization_id, 'delete', actor_user_id) then
        raise exception '%', jsonb_build_object('kind', 'not-found', 'permission', 'delete org_member', 'objectType', 'organization', 'objectId', organization_id);
    end if;
    perform remove_member_from_org_unchecked(user_to_remove_id, organization_id);
end;
$$;

revoke execute on function remove_member_from_org from anon;

-- Define org_secrets.
--
-- Following
-- https://supabase.com/docs/guides/database/vault
-- https://supabase.com/blog/transparent-column-encryption-with-postgres#one-key-id-per-row-with-associated-data

CREATE SCHEMA secrets;

create table secrets.org_secrets (
    id uuid not null primary key default uuid_generate_v4(),
    created timestamp with time zone default current_timestamp,
    key_id uuid not null default (pgsodium.create_key()).id,
    nonce bytea default pgsodium.crypto_aead_det_noncegen(),
    org_id uuid not null references organizations,
    name text not null,
    secret text,
    type text,
    metadata jsonb,
    updated_at timestamp with time zone
);

create index on secrets.org_secrets(org_id, type);
create unique index on secrets.org_secrets (org_id, name);

-- ASSOCIATED means that if org_id or name are changed, then the key is
-- unrecoverable.
security label
    for pgsodium
    on column secrets.org_secrets.secret
    is 'ENCRYPT WITH KEY COLUMN key_id ASSOCIATED (org_id, name) NONCE nonce';

alter table secrets.org_secrets enable row level security;

-- Resource definitions and counts. We manage resources at the organization
-- level, and for time-windowed resources, keep track of them at the granularity
-- of days.

-- max_over_window is a type that imposes a maximum limit on some resource over
-- a particular time window in days. The time window specifies the number of
-- days into the past from the current day (inclusive).
create type max_over_window_underlying as (
	window_size_days integer,
	max_value bigint
);

create domain max_over_window as max_over_window_underlying check (
	(value) is null or (
        (value).window_size_days is not null
        and (value).window_size_days > 0
        and (value).max_value is not null
        and (value).max_value >= 0
    )
);

-- max_over_calendar_months is a type that imposes a maximum limit on some
-- resource over a number of calendar months.
create type max_over_calendar_months_underlying as (
	window_size_months integer,
	max_value bigint
);

create domain max_over_calendar_months as max_over_calendar_months_underlying check (
	(value) is null or (
        (value).window_size_months is not null
        and (value).window_size_months > 0
        and (value).max_value is not null
        and (value).max_value >= 0
    )
);

create table resources (
    org_id uuid not null primary key references organizations,
    -- Controls whether an org has the ability to toggle its experiments from
    -- public to private. If true, they must copy the public experiment to a new
    -- private one.
    forbid_toggle_experiment_public_to_private boolean,
    -- Controls the number of row actions (inserts, updates, deletes) that can
    -- occur across all private experiments in the org. Row action counts are
    -- tracked in the `resource_counts` table. No limit if null.
    num_private_experiment_row_actions max_over_window,
    forbid_insert_datasets boolean,
    forbid_insert_prompt_sessions boolean,
    forbid_access_sql_explorer boolean,
    num_production_log_row_actions max_over_window,
    num_dataset_row_actions max_over_window,
    num_log_bytes max_over_window,
    num_private_experiment_row_actions_calendar_months max_over_calendar_months,
    num_production_log_row_actions_calendar_months max_over_calendar_months,
    num_dataset_row_actions_calendar_months max_over_calendar_months,
    num_log_bytes_calendar_months max_over_calendar_months
);

create table resource_counts (
    id uuid default extensions.uuid_generate_v4() not null primary key,
    org_id uuid not null references organizations,
    -- This is expected to be the textual name of the column in `resources` that
    -- we are counting.
    resource_name text not null,
    date_bucket date,
    shard_key integer,
    -- Note: it is important for operations on this resource to be atomic,
    -- commutative, and reversible. It is also important for values to be exact
    -- (no rounding errors). The bigint type satisfies these requirements and
    -- should work for most kinds of counts.
    count bigint
);

create unique index on resource_counts (org_id, resource_name, date_bucket, shard_key) nulls not distinct;

alter table resources enable row level security;
alter table resource_counts enable row level security;

-- Resource tiers are used to name a certain combination of resource settings
-- for different categories of orgs. We do not currently store the resource tier
-- name in the resource definition, since we might make instance-specific
-- modifications for different orgs.
create type resource_tier as enum (
    'free',
    'unlimited',
    'edu'
);

create function insert_resource_definition(
    org_id uuid,
    tier resource_tier)
returns void
language plpgsql
security definer
as $$
declare
    _insert_row resources%rowtype;
begin
    _insert_row.org_id := org_id;
    case
        when tier = 'free' then
            _insert_row.forbid_toggle_experiment_public_to_private := true;
            _insert_row.num_private_experiment_row_actions := row(7, 250000);
            _insert_row.num_production_log_row_actions := row(7, 250000);
            _insert_row.num_dataset_row_actions := row(7, 250000);
            _insert_row.num_log_bytes := row(7, 250000000);
            _insert_row.num_private_experiment_row_actions_calendar_months := row(1, 1000000);
            _insert_row.num_production_log_row_actions_calendar_months := row(1, 1000000);
            _insert_row.num_dataset_row_actions_calendar_months := row(1, 1000000);
            _insert_row.num_log_bytes_calendar_months := row(1, 1075000000);
            _insert_row.forbid_insert_datasets := false;
            _insert_row.forbid_insert_prompt_sessions := false;
            _insert_row.forbid_access_sql_explorer := true;
        when tier = 'unlimited' then
    end case;

    insert into resources
    select _insert_row.*;
end;
$$;

create function determine_resource_tier(user_id uuid)
returns resource_tier
language plpgsql
stable
as $$
declare
    _user_email text;
begin
    select email
    from users
    where id = user_id
    into _user_email;

    if not found then
        raise exception '%', jsonb_build_object('kind', 'not-found', 'permission', 'read', 'objectType', 'user', 'objectId', user_id);
    end if;

    return 'free';
end;
$$;

revoke execute on function determine_resource_tier from anon;

create function check_org_name_conflict(org_name text)
returns boolean
language sql
security definer
stable
as $$
    select
    EXISTS (
        SELECT 1
        FROM organizations
        WHERE
            organizations.name = org_name
    )
END;
$$;

grant execute on function check_org_name_conflict to anon;

create function register_org(auth_id uuid, org_name text)
returns uuid
language plpgsql
security definer
as $$
declare
    _user_id uuid := get_user_id_by_auth_id(register_org.auth_id);
    _org_id uuid;
begin
    -- Anonymous users cannot create orgs.
    if _user_id is null or _user_id = anon_user_id() then
        raise exception '%', jsonb_build_object('kind', 'http-error', 'code', 401, 'message', 'Unauthorized users cannot create orgs');
    end if;

    insert into
        organizations(name)
    values
        (org_name)
    returning
        id
    into
        _org_id
    ;

    perform add_member_to_org_unchecked(_user_id, _org_id, array [get_group_id(_org_id, 'Owners')]);
    perform insert_resource_definition(_org_id, determine_resource_tier(_user_id));
    return _org_id;
end;
$$;

revoke execute on function register_org from public, anon;

-- Override the current date for a particular org. Used for testing.
create table _braintrust_testing_mock_current_date (
    id uuid default uuid_generate_v4() not null primary key,
    org_id uuid not null references organizations,
    _mock_current_date date
);

alter table _braintrust_testing_mock_current_date enable row level security;

-- Function to check/update a resource of type max_over_window. Will update
-- resource_counts even if there is no limit. Will raise an exception if any of
-- the resource counts are exceeded.
--
-- Return schema:
-- {
--     -- The increment of each updated row in the table. This could be used to
--     -- reverse the update in a future (non-transactional) query.
--     [resource_count_id]: number;
-- }

-- Helper types

create type org_id_and_count_underlying as (
    org_id uuid,
    count bigint
);

create domain org_id_and_count as org_id_and_count_underlying check (
    (value) is not null
    and (value).org_id is not null
    and (value).count is not null
    and (value).count >= 0
);

create type max_value_resource_violation as (
    org_id uuid,
    total_value bigint,
    max_value bigint
);

-- Note: this function is deprecated since we no longer use day-based ranges for
-- any of our resource counts. It is largely duplicating functionality in
-- update_windowed_resource_counts_calendar_months.
create function update_windowed_resource_counts(
    -- Should match the column name in 'resources' that we are working with.
    resource_name_col text,
    num_shards integer,
    org_id_and_counts org_id_and_count[],
    auth_id uuid)
returns void
language plpgsql
security definer
as $$
declare
    _now_date date := current_date;
    _auth_violation_orgs uuid[];
    _resource_violations max_value_resource_violation[];
begin
    -- Check for not null inputs.
    if (resource_name_col is null) then
      raise exception 'Must provide resource_name_col';
    end if;

    if (num_shards is null or num_shards <= 0) then
      raise exception 'Must provide positive num_shards';
    end if;

    if (org_id_and_counts is null) then
      raise exception 'Must provide org_id_and_counts';
    end if;

    -- Check that the user named by `auth_id` belongs to all orgs being updated.
    with
    valid_org_ids as (
    select
        org_id
    from
        unnest(org_id_and_counts)
        join members using (org_id)
        join users on members.user_id = users.id
    where
        users.auth_id = update_windowed_resource_counts.auth_id
    )
    select array_agg(org_id)
    from unnest(org_id_and_counts)
    where org_id not in (select org_id from valid_org_ids)
    into _auth_violation_orgs
    ;

    if (array_length(_auth_violation_orgs, 1) > 0) then
        raise exception '%', jsonb_build_object('kind', 'http-error', 'code', 400, 'message', 'User does not have permission to update resources for all requested orgs');
    end if;

    -- Upsert each row in org_id_and_counts.
    with
    rows_to_insert as (
    select
        org_id,
        resource_name_col resource_name,
        coalesce(_mock_current_date, _now_date) date_bucket,
        floor(random() * num_shards) as shard_key,
        count
    from
        unnest(org_id_and_counts)
        left join _braintrust_testing_mock_current_date using (org_id)
    )
    insert into resource_counts(org_id, resource_name, date_bucket, shard_key, count)
    select *
    from rows_to_insert
    order by org_id, resource_name, date_bucket, shard_key
    on conflict (org_id, resource_name, date_bucket, shard_key)
    do update set count = resource_counts.count + excluded.count
    ;

    -- For error check, compute the total value of each resource count, windowed
    -- over the last "window_size_days" days of the corresponding resource (ignore
    -- orgs with null resource definition). Filter for rows where total_value >
    -- max_value. Aggregate these into an array and raise an exception if the array
    -- is nonempty.
    --
    -- We must generate this query dynamically because the column name of the
    -- resource is defined as `resource_name_col`.
    --
    -- Note: This sum may race with other concurrent resource updates. These
    -- concurrent updates will not see the results of the concurrent uncommitted
    -- transactions, meaning we may let some inserts through which violate the
    -- resource limitation. This should be okay, since it favors the user and
    -- we should trigger the resource violation the next time they insert.
    execute format(
        'with'
        ' current_dates as ('
        '   select'
        '     org_id,'
        '     coalesce(_mock_current_date, current_date) as current_date_value'
        '   from'
        '     unnest($1) as u(org_id)'
        '     left join _braintrust_testing_mock_current_date using (org_id)'
        ' ),'
        ' resource_total_counts as ('
        ' select'
        '     rc.org_id,'
        '     SUM(rc.count) total_value,'
        '     (r.%I).max_value,'
        '     cd.current_date_value'
        ' from'
        '     resource_counts rc'
        '     join resources r on rc.org_id = r.org_id'
        '     join current_dates cd on rc.org_id = cd.org_id'
        ' where'
        '     rc.resource_name = $2'
        '     and (r.%I) is not null'
        '     and rc.date_bucket > (cd.current_date_value - (r.%I).window_size_days)'
        '     and rc.date_bucket <= cd.current_date_value'
        ' group by'
        '     rc.org_id, max_value, cd.current_date_value'
        ' )'
        ' select array_agg(row(org_id, total_value, max_value)'
        '                     ::max_value_resource_violation)'
        ' from resource_total_counts'
        ' where total_value > max_value'
        , resource_name_col, resource_name_col, resource_name_col)
    into _resource_violations
    using org_id_and_counts, resource_name_col
    ;

    if (array_length(_resource_violations, 1) > 0) then
        -- The text of this error message is used to identify the error in the
        -- data backend. If you change it, make sure that the filter in
        -- api-ts/src/resource_check.ts is still valid.
        raise exception '%', jsonb_build_object('kind', 'http-error', 'code', 400, 'message', format('Violations of resource constraint %s: %s', resource_name_col, _resource_violations));
    end if;
end;
$$;

revoke execute on function update_windowed_resource_counts from anon;

-- Note: this function is largely a mirror of update_windowed_resource_counts.
-- We should eventually be able to remove that function once we have migrated
-- all resource counts to calendar months.
create function update_windowed_resource_counts_calendar_months(
    -- Should match the column name in 'resources' that we are working with.
    resource_name_col text,
    num_shards integer,
    org_id_and_counts org_id_and_count[],
    auth_id uuid)
returns void
language plpgsql
security definer
as $$
declare
    _now_date date := current_date;
    _auth_violation_orgs uuid[];
    _resource_violations max_value_resource_violation[];
begin
    -- Check for not null inputs.
    if (resource_name_col is null) then
      raise exception 'Must provide resource_name_col';
    end if;

    if (num_shards is null or num_shards <= 0) then
      raise exception 'Must provide positive num_shards';
    end if;

    if (org_id_and_counts is null) then
      raise exception 'Must provide org_id_and_counts';
    end if;

    -- Check that the user named by `auth_id` belongs to all orgs being updated.
    with
    valid_org_ids as (
    select
        org_id
    from
        unnest(org_id_and_counts)
        join members using (org_id)
        join users on members.user_id = users.id
    where
        users.auth_id = update_windowed_resource_counts_calendar_months.auth_id
    )
    select array_agg(org_id)
    from unnest(org_id_and_counts)
    where org_id not in (select org_id from valid_org_ids)
    into _auth_violation_orgs
    ;

    if (array_length(_auth_violation_orgs, 1) > 0) then
        raise exception '%', jsonb_build_object('kind', 'http-error', 'code', 400, 'message', 'User does not have permission to update resources for all requested orgs');
    end if;

    -- Upsert each row in org_id_and_counts.
    with
    rows_to_insert as (
    select
        org_id,
        resource_name_col resource_name,
        date_trunc('month', coalesce(_mock_current_date, _now_date)) date_bucket,
        floor(random() * num_shards) as shard_key,
        count
    from
        unnest(org_id_and_counts)
        left join _braintrust_testing_mock_current_date using (org_id)
    )
    insert into resource_counts(org_id, resource_name, date_bucket, shard_key, count)
    select *
    from rows_to_insert
    order by org_id, resource_name, date_bucket, shard_key
    on conflict (org_id, resource_name, date_bucket, shard_key)
    do update set count = resource_counts.count + excluded.count
    ;

    -- For error check, compute the total value of each resource count, windowed
    -- over the last "window_size_months" months of the corresponding resource (ignore
    -- orgs with null resource definition). Filter for rows where total_value >
    -- max_value. Aggregate these into an array and raise an exception if the array
    -- is nonempty.
    --
    -- We must generate this query dynamically because the column name of the
    -- resource is defined as `resource_name_col`.
    --
    -- Note: This sum may race with other concurrent resource updates. These
    -- concurrent updates will not see the results of the concurrent uncommitted
    -- transactions, meaning we may let some inserts through which violate the
    -- resource limitation. This should be okay, since it favors the user and
    -- we should trigger the resource violation the next time they insert.
    execute format(
        'with'
        ' current_dates as ('
        '   select'
        '     org_id,'
        '     date_trunc(''month'', coalesce(_mock_current_date, $3)) as current_month_date'
        '   from'
        '     unnest($1) as u(org_id)'
        '     left join _braintrust_testing_mock_current_date using (org_id)'
        ' ),'
        ' resource_total_counts as ('
        ' select'
        '     rc.org_id,'
        '     SUM(rc.count) total_value,'
        '     (r.%I).max_value,'
        '     cd.current_month_date'
        ' from'
        '     resource_counts rc'
        '     join resources r on rc.org_id = r.org_id'
        '     join current_dates cd on rc.org_id = cd.org_id'
        ' where'
        '     rc.resource_name = $2'
        '     and (r.%I) is not null'
        '     and rc.date_bucket > (cd.current_month_date - make_interval(months => (r.%I).window_size_months))'
        '     and rc.date_bucket <= cd.current_month_date'
        ' group by'
        '     rc.org_id, max_value, cd.current_month_date'
        ' )'
        ' select array_agg(row(org_id, total_value, max_value)'
        '                     ::max_value_resource_violation)'
        ' from resource_total_counts'
        ' where total_value > max_value'
        , resource_name_col, resource_name_col, resource_name_col)
    into _resource_violations
    using org_id_and_counts, resource_name_col, _now_date
    ;

    if (array_length(_resource_violations, 1) > 0) then
        -- The text of this error message is used to identify the error in the
        -- data backend. If you change it, make sure that the filter in
        -- api-ts/src/resource_check.ts is still valid.
        raise exception '%', jsonb_build_object('kind', 'http-error', 'code', 400, 'message', format('Violations of resource constraint %s: %s', resource_name_col, _resource_violations));
    end if;
end;
$$;

revoke execute on function update_windowed_resource_counts_calendar_months from anon;

-- Function to check/update resource counts upon insertion. Throws an exception
-- if any of the resource counts are exceeded.
--
-- Input schema:
-- {
--     experiments: {
--         [experiment_id]: {
--             num_row_actions: number;
--         }
--     },
--     logs: {
--         [org_id]: {
--             num_row_actions: number;
--             num_row_bytes: number;
--         }
--     }
--     datasets: {
--         [dataset_id]: {
--             num_row_actions: number;
--         }
--     }
--     additional_org_ids: [org_id]
-- }
--
-- Return schema:
-- {
--     "is_unlimited": {
--         [org_id]: boolean;
--     };
-- }
create function update_resource_counts_for_insert(
    input jsonb,
    num_shards integer,
    auth_id uuid)
returns jsonb
language plpgsql
security definer
as $$
declare
    _user_id uuid := get_user_id_by_auth_id(auth_id);
    _num_private_experiment_row_actions_org_id_and_counts org_id_and_count[];
    _num_production_log_row_actions_org_id_and_counts org_id_and_count[];
    _num_dataset_row_actions_org_id_and_counts org_id_and_count[];
    _num_log_bytes_org_id_and_counts org_id_and_count[];
    _use_calendar_months boolean := coalesce(get_app_config_boolean('use_calendar_months'), false);
begin
    -- Process resource 'num_private_experiment_row_actions'.

    -- 1. Grab the set of private experiments out of the input.
    --
    -- 2. Join them up to get their containing org. Collect the number of row
    -- actions per org. Store these into
    -- _num_private_experiment_row_actions_org_id_and_counts.
    --
    -- 3. Invoke `update_windowed_resource_counts`.
    with
    input_experiments as (
    select
       key::uuid experiment_id,
       (value->'num_row_actions')::bigint num_row_actions
    from
        jsonb_each(coalesce(input->'experiments', '{}'::jsonb))
     ),
     num_row_actions_per_org as (
     select
        row(organizations.id,
            SUM(input_experiments.num_row_actions))
            ::org_id_and_count r
    from
        input_experiments
        join experiments on input_experiments.experiment_id = experiments.id
        join projects on experiments.project_id = projects.id
        join organizations on projects.org_id = organizations.id
    where
        experiments.deleted_at is null
        and projects.deleted_at is null
        and not has_under_project_acl('experiment', experiments.id, experiments.project_id, 'read', anon_user_id())
    group by
      organizations.id
    )
    select coalesce(array_agg(r), array [] :: org_id_and_count[])
    from num_row_actions_per_org
    into _num_private_experiment_row_actions_org_id_and_counts
    ;

    if _use_calendar_months then
        perform update_windowed_resource_counts_calendar_months(
            resource_name_col => 'num_private_experiment_row_actions_calendar_months',
            num_shards => num_shards,
            org_id_and_counts => _num_private_experiment_row_actions_org_id_and_counts,
            auth_id => auth_id);
    else
        perform update_windowed_resource_counts(
            resource_name_col => 'num_private_experiment_row_actions',
            num_shards => num_shards,
            org_id_and_counts => _num_private_experiment_row_actions_org_id_and_counts,
            auth_id => auth_id);
    end if;
    -- Process resource 'num_production_log_row_actions'.

    -- 1. Grab the set of logs out of the input.
    --
    -- 2. Collect the logs recorded per org. Store these into
    -- _num_production_log_row_actions_org_id_and_counts.
    --
    -- 3. Invoke `update_windowed_resource_counts`.
    with
    input_logs as (
    select
        key::uuid org_id,
        (value->'num_row_actions')::bigint num_row_actions
    from
        jsonb_each(coalesce(input->'logs', '{}'::jsonb))
    ),
    num_row_actions_per_org as (
    select
        row(org_id, SUM(num_row_actions))::org_id_and_count r
    from
        input_logs
    group by
        org_id
    )
    select coalesce(array_agg(r), array [] :: org_id_and_count[])
    from num_row_actions_per_org
    into _num_production_log_row_actions_org_id_and_counts
    ;

    if _use_calendar_months then
        perform update_windowed_resource_counts_calendar_months(
            resource_name_col => 'num_production_log_row_actions_calendar_months',
            num_shards => num_shards,
            org_id_and_counts => _num_production_log_row_actions_org_id_and_counts,
            auth_id => auth_id);
    else
        perform update_windowed_resource_counts(
            resource_name_col => 'num_production_log_row_actions',
            num_shards => num_shards,
            org_id_and_counts => _num_production_log_row_actions_org_id_and_counts,
            auth_id => auth_id);
    end if;

    -- Process resource 'num_log_bytes'.

    -- 1. Grab the set of logs out of the input.
    --
    -- 2. Collect the total bytes recorded per org. Store these into
    -- _num_log_bytes_org_id_and_counts.
    --
    -- 3. Invoke `update_windowed_resource_counts`.
    with
    input_log_bytes as (
    select
        key::uuid org_id,
        coalesce((value->'num_row_bytes')::bigint, 0) num_row_bytes
    from
        jsonb_each(coalesce(input->'logs', '{}'::jsonb))
    ),
    num_row_bytes_per_org as (
    select
        row(org_id, SUM(num_row_bytes))::org_id_and_count r
    from
        input_log_bytes
    group by
        org_id
    )
    select coalesce(array_agg(r), array [] :: org_id_and_count[])
    from num_row_bytes_per_org
    into _num_log_bytes_org_id_and_counts
    ;

    if _use_calendar_months then
        perform update_windowed_resource_counts_calendar_months(
            resource_name_col => 'num_log_bytes_calendar_months',
            num_shards => num_shards,
            org_id_and_counts => _num_log_bytes_org_id_and_counts,
            auth_id => auth_id);
    else
        perform update_windowed_resource_counts(
            resource_name_col => 'num_log_bytes',
            num_shards => num_shards,
            org_id_and_counts => _num_log_bytes_org_id_and_counts,
            auth_id => auth_id);
    end if;

    -- Process resource 'num_dataset_row_actions'.

    -- 1. Grab the set of datasets out of the input.
    --
    -- 2. Join them up to get their containing org. Collect the number of row
    -- actions per org. Store these into
    -- _num_dataset_row_actions_org_id_and_counts.
    --
    -- 3. Invoke `update_windowed_resource_counts`.
    with
    input_datasets as (
    select
       key::uuid dataset_id,
       (value->'num_row_actions')::bigint num_row_actions
    from
        jsonb_each(coalesce(input->'datasets', '{}'::jsonb))
     ),
     num_row_actions_per_org as (
     select
        row(organizations.id,
            SUM(input_datasets.num_row_actions))
            ::org_id_and_count r
    from
        input_datasets
        join datasets on input_datasets.dataset_id = datasets.id
        join projects on datasets.project_id = projects.id
        join organizations on projects.org_id = organizations.id
    where
        datasets.deleted_at is null
        and projects.deleted_at is null
    group by
        organizations.id
    )
    select coalesce(array_agg(r), array [] :: org_id_and_count[])
    from num_row_actions_per_org
    into _num_dataset_row_actions_org_id_and_counts
    ;

    if _use_calendar_months then
        perform update_windowed_resource_counts_calendar_months(
            resource_name_col => 'num_dataset_row_actions_calendar_months',
            num_shards => num_shards,
            org_id_and_counts => _num_dataset_row_actions_org_id_and_counts,
            auth_id => auth_id);
    else
        perform update_windowed_resource_counts(
            resource_name_col => 'num_dataset_row_actions',
            num_shards => num_shards,
            org_id_and_counts => _num_dataset_row_actions_org_id_and_counts,
            auth_id => auth_id);
    end if;

    -- Finally, return whether or not the orgs considered have unlimited
    -- resource counts, which can allow skipping future resource checks.
    return (
        with
        all_org_ids as (
            select org_id from unnest(_num_private_experiment_row_actions_org_id_and_counts)
            union all
            select org_id from unnest(_num_production_log_row_actions_org_id_and_counts)
            union all
            select org_id from unnest(_num_dataset_row_actions_org_id_and_counts)
            union all
            select value::uuid as org_id from jsonb_array_elements_text(coalesce(input->'additional_org_ids', '[]'::jsonb))
        ),
        org_id_is_unlimited as (
            select
                organizations.id org_id,
                (
                    -- No resource entry for an org implies unlimited.
                    resources.org_id isnull
                    or (
                        not _use_calendar_months
                        and resources.num_private_experiment_row_actions isnull
                        and resources.num_production_log_row_actions isnull
                        and resources.num_dataset_row_actions isnull
                        and resources.num_log_bytes isnull
                    ) or (
                        _use_calendar_months
                        and resources.num_private_experiment_row_actions_calendar_months isnull
                        and resources.num_production_log_row_actions_calendar_months isnull
                        and resources.num_dataset_row_actions_calendar_months isnull
                        and resources.num_log_bytes_calendar_months isnull
                    )
                ) is_unlimited
            from
                organizations left join resources on organizations.id = resources.org_id
                join members on members.org_id = organizations.id
            where
                organizations.id in (select org_id from all_org_ids)
                and members.user_id = _user_id
        )
        select jsonb_build_object(
            'is_unlimited', coalesce(jsonb_object_agg(org_id::text, is_unlimited), '{}'::jsonb))
        from org_id_is_unlimited
    );
end;
$$;

revoke execute on function update_resource_counts_for_insert from anon;

-- Define projects, datasets, and experiments.

create table projects (
    id uuid default uuid_generate_v4() not null primary key,
    org_id uuid not null references organizations,
    name text not null,
    created timestamp with time zone default current_timestamp,
    user_id uuid references users,
    deleted_at timestamp with time zone,
    settings jsonb,
    internal_metadata jsonb
);

create index on projects (org_id);
create unique index on projects (org_id, name, deleted_at) nulls not distinct;

alter table projects enable row level security;

create table datasets (
    id uuid not null primary key default uuid_generate_v4(),
    project_id uuid not null references projects,
    name text not null,
    description text,
    created timestamp with time zone default current_timestamp,
    deleted_at timestamp with time zone,
    user_id uuid references users
);

create index on datasets (project_id);
create unique index on datasets (project_id, name, deleted_at) nulls not distinct;

alter table datasets enable row level security;
alter table datasets add metadata jsonb;

create table experiments (
    id uuid not null primary key default uuid_generate_v4(),
    project_id uuid not null references projects,
    name text not null,
    description text,
    created timestamp with time zone default current_timestamp,
    repo_info jsonb,
    commit text generated always as ((repo_info ->> 'commit'::text)) stored,
    base_exp_id uuid references experiments,
    deleted_at timestamp with time zone,
    dataset_id uuid references datasets,
    dataset_version text,
    user_id uuid references users,
    metadata jsonb,
    tags text[]
);

create index experiments_project_id_idx on experiments (project_id);
create index commit_idx on experiments (project_id, commit, created);
create unique index on experiments (project_id, name, deleted_at) nulls not distinct;
create index metadata_gin_idx on experiments using gin (metadata);
create index experiments_tags_gin_idx on experiments using gin (tags);

alter table experiments enable row level security;

create function get_or_create_project(
    auth_id uuid,
    project_name text,
    org_id uuid)
returns uuid
language plpgsql
security definer
as $$
declare
    _user_id uuid = get_user_id_by_auth_id(auth_id);
    _project_id uuid;
begin
    -- Try to find the project. If found, check if we have the appropriate
    -- permissions and then return it.
    select id
    from projects
    where
        projects.name = project_name
        and projects.org_id = get_or_create_project.org_id
        and projects.deleted_at isnull
    into _project_id
    ;

    if _project_id is not null then
      if not has_project_acl('project', _project_id, 'read', _user_id) then
          raise exception '%', jsonb_build_object('kind', 'not-found', 'permission', 'read', 'objectType', 'project', 'objectId', project_name);
      end if;
      return _project_id;
    end if;

    -- Project does not exist. Check if we have permissions to create it, and
    -- then do that.

    if not has_under_organization_acl('org_project', org_id, org_id, 'create', _user_id) then
        raise exception '%', jsonb_build_object('kind', 'not-found', 'permission', 'create', 'objectType', 'org_project', 'objectId', org_id);
    end if;

    insert into projects(org_id, name, user_id)
    values (get_or_create_project.org_id, project_name, _user_id)
    on conflict do nothing
    returning id into _project_id
    ;

    if _project_id is null then
        -- Duplicate key.
        select id
        from projects
        where
            projects.name = project_name
            and projects.org_id = get_or_create_project.org_id
            and projects.deleted_at isnull
        into _project_id
        ;
        if not found then
            raise exception 'Project % likely deleted concurrently with creation', project_name;
        end if;
    else
        perform register_acl_unchecked(
            _object_type => 'project', _object_id => _project_id,
            _user_id => _user_id, _role_id => get_owner_role_id());
    end if;
    return _project_id;
end;
$$;
revoke execute on function get_or_create_project from public, anon;

create function register_project(
    auth_id uuid,
    project_name text,
    org_id uuid)
returns jsonb
language plpgsql
security definer
as $$
declare
    _project_id uuid := get_or_create_project(auth_id, project_name, org_id);
begin
    return (
        select jsonb_build_object('project', projects)
        from projects
        where projects.id = _project_id
    );
end;
$$;

revoke execute on function register_project from public, anon;

create function register_dataset(
    auth_id uuid,
    org_id uuid,
    project_id uuid default null,
    project_name text default null,
    dataset_name text default null,
    description text default null,
    update boolean default null,
    metadata jsonb default null)
returns jsonb
language plpgsql
security definer
as $$
declare
    -- Initialize meaningful defaults.
    _dataset_name text := coalesce(dataset_name, 'logs');
    _update boolean := coalesce(update, false);

    _user_id uuid := get_user_id_by_auth_id(auth_id);
    _project_id uuid;
    _dataset_id uuid;
    _forbid_insert_datasets boolean;
    _found_existing boolean := false;
begin
    -- Assign the project_id.
    if register_dataset.project_id is not null then
        -- Check that the project exists.
        select projects.id into _project_id
        from projects
        where
            projects.id = register_dataset.project_id
            and projects.deleted_at isnull
        ;
        if not found then
            raise exception '%', jsonb_build_object('kind', 'not-found', 'permission', 'read', 'objectType', 'project', 'objectId', register_dataset.project_id);
        end if;
    else
        _project_id = get_or_create_project(auth_id, project_name, org_id);
    end if;

    -- First check if the dataset already exists.
    select datasets.id
    from
        datasets
        join projects on datasets.project_id = projects.id
    where
        datasets.project_id = _project_id
        and datasets.name = _dataset_name
        and datasets.deleted_at isnull
        and projects.deleted_at isnull
    into _dataset_id
    ;

    if _dataset_id is not null then
        -- Duplicate key. Check for read permissions.
        _found_existing := true;
        if not has_under_project_acl('dataset', _dataset_id, _project_id, 'read', _user_id) then
            raise exception '%', jsonb_build_object('kind', 'not-found', 'permission', 'read', 'objectType', 'dataset', 'objectId', _dataset_id);
        end if;

        if _update then
            -- Check for update permissions on the dataset.
            if not has_under_project_acl('dataset', _dataset_id, _project_id, 'update', _user_id) then
                raise exception '%', jsonb_build_object('kind', 'not-found', 'permission', 'update', 'objectType', 'dataset', 'objectId', _dataset_id);
            end if;

            update datasets
            set
                description = register_dataset.description,
                metadata = register_dataset.metadata
            where id = _dataset_id;
        end if;
    else
        -- Check for create permissions and create the dataset.
        select resources.forbid_insert_datasets
        from projects join resources using (org_id)
        where projects.id = _project_id
        into _forbid_insert_datasets
        ;

        if _forbid_insert_datasets or not has_project_acl('dataset', _project_id, 'create', _user_id) then
            raise exception '%', jsonb_build_object('kind', 'not-found', 'permission', 'create dataset', 'objectType', 'project', 'objectId', _project_id);
        end if;

        insert into datasets(project_id, name, description, user_id, metadata)
        values (_project_id, _dataset_name, register_dataset.description, _user_id, register_dataset.metadata)
        on conflict do nothing
        returning id into _dataset_id
        ;

        if _dataset_id is null then
            -- Dataset was likely created concurrently. Just fetch it.
            _found_existing := true;
            select datasets.id from datasets
            where datasets.project_id = _project_id and datasets.name = _dataset_name
            into _dataset_id
            ;

            if not found then
              raise exception 'Dataset % (under project %) likely deleted concurrently with creation',
                  _dataset_name, _project_id;
            end if;
        else
            perform register_acl_unchecked(
                _object_type => 'dataset', _object_id => _dataset_id,
                _user_id => _user_id, _role_id => get_owner_role_id());
        end if;
    end if;

    return (
        select jsonb_build_object('project', projects, 'dataset', datasets, 'found_existing', _found_existing)
        from projects join datasets on projects.id = datasets.project_id
        where datasets.id = _dataset_id
    );
end;
$$;

revoke execute on function register_dataset from public, anon;

create function insert_experiment(
    user_id uuid,
    project_id uuid,
    name text,
    description text,
    repo_info jsonb,
    base_exp_id uuid,
    dataset_id uuid,
    dataset_version text,
    metadata jsonb,
    tags text[]
)
returns uuid
language plpgsql
security definer
as $$
declare
    _experiment_id uuid;
begin
    if not has_project_acl('experiment', project_id, 'create', user_id) then
        raise exception '%', jsonb_build_object('kind', 'not-found', 'permission', 'create experiment', 'objectType', 'project', 'objectId', project_id);
    end if;

    insert into
        experiments(project_id, name, description, repo_info, base_exp_id,
                    dataset_id, dataset_version, user_id, metadata, tags)
    values
        (project_id, name, description, repo_info, base_exp_id, dataset_id,
         dataset_version, user_id, metadata, tags)
    on conflict
        do nothing
    returning
        id into _experiment_id
    ;

    return _experiment_id;
end;
$$;
revoke execute on function insert_experiment from public, anon;

create table org_settings (
    org_id uuid primary key references organizations,
    git_metadata jsonb,
    loop_allowed_models jsonb
);

alter table org_settings enable row level security;

create function sanitize_repo_info(
    repo_info jsonb,
    org_id uuid
)
returns jsonb
language plpgsql
security definer
stable
as $$
declare
    _repo_info jsonb;
    _git_metadata_settings jsonb;
    _fields text[];
begin
    select git_metadata
    from org_settings
    where org_settings.org_id = sanitize_repo_info.org_id
    into _git_metadata_settings;

    if (_git_metadata_settings is null or _git_metadata_settings->>'collect' = 'all') then
        _repo_info := repo_info;
    end if;

    if (_git_metadata_settings->>'collect' = 'some') then
        select array_agg(fields)
        from jsonb_array_elements_text(COALESCE(_git_metadata_settings->'fields', '[]')) fields
        into _fields;

        select jsonb_object_agg(key, value)
        from jsonb_each(repo_info)
        where key = any(_fields)
        into _repo_info;
    end if;

    return _repo_info;
end;
$$;

revoke execute on function sanitize_repo_info from public, anon;

create function mark_experiment_as_public(
    experiment_id uuid,
    project_id uuid,
    performing_user_id uuid)
returns void
language plpgsql
security definer
as $$
begin
    if not has_under_project_acl('experiment', experiment_id, project_id, 'create_acls', performing_user_id) then
        raise exception '%', jsonb_build_object('kind', 'not-found', 'permission', 'create_acls', 'objectType', 'experiment', 'objectId', experiment_id);
    end if;
    perform register_acl_unchecked(
        _object_type => 'experiment', _object_id => experiment_id,
        _user_id => anon_user_id(), _permission => 'read',
        _restrict_object_type => 'experiment');
end;
$$;

revoke execute on function mark_experiment_as_public from public, anon;

create function mark_experiment_as_nonpublic(
    experiment_id uuid,
    project_id uuid,
    performing_user_id uuid)
returns void
language plpgsql
security definer
as $$
begin
    if not has_under_project_acl('experiment', experiment_id, project_id, 'delete_acls', performing_user_id) then
        raise exception '%', jsonb_build_object('kind', 'not-found', 'permission', 'delete_acls', 'objectType', 'experiment', 'objectId', experiment_id);
    end if;
    delete from acls
    where id = find_acl_id(
        _object_type => 'experiment',
        _object_id => experiment_id,
        _user_id => anon_user_id(),
        _permission => 'read',
        _restrict_object_type => 'experiment')
    ;
end;
$$;

revoke execute on function mark_experiment_as_nonpublic from public, anon;

create function register_experiment(
    auth_id uuid,
    org_id uuid,
    project_id uuid default null,
    project_name text default null,
    experiment_name text default null,
    description text default null,
    update boolean default null,
    repo_info jsonb default null,
    base_exp_id uuid default null,
    base_experiment text default null,
    ancestor_commits text[] default null,
    dataset_id uuid default null,
    dataset_version text default null,
    public boolean default null,
    metadata jsonb default null,
    tags text[] default null)
returns jsonb
language plpgsql
security definer
as $$
declare
    -- Initialize meaningful defaults.
    _update boolean := coalesce(update, false);
    _public boolean := coalesce(public, false);

    _user_id uuid := get_user_id_by_auth_id(auth_id);
    _repo_info jsonb := sanitize_repo_info(coalesce(repo_info, '{}'), org_id);
    _project_id uuid;
    _base_exp_id uuid;
    _base_exp_project_id uuid;
    _found_base_exp_id_from_ancestor_commits boolean = false;
    _conflicting_experiment_id uuid;
    _inserted_experiment_id uuid;
begin
    -- Assign the project_id.
    if register_experiment.project_id is not null then
        -- Check that the project exists.
        select projects.id into _project_id
        from projects
        where
            projects.id = register_experiment.project_id
            and projects.deleted_at isnull
        ;
        if not found then
            raise exception '%', jsonb_build_object('kind', 'not-found', 'permission', 'read', 'objectType', 'project', 'objectId', register_experiment.project_id);
        end if;
    else
        _project_id = get_or_create_project(auth_id, project_name, org_id);
    end if;

    if base_exp_id is not null then
        -- Check that the base experiment exists.
        select experiments.id, experiments.project_id into _base_exp_id, _base_exp_project_id
        from
            experiments join projects on experiments.project_id = projects.id
        where
            experiments.id = register_experiment.base_exp_id
            and experiments.deleted_at isnull
            and projects.deleted_at isnull
        ;
        if not found then
            raise exception '%', jsonb_build_object('kind', 'not-found', 'permission', 'read', 'objectType', 'experiment', 'objectId', register_experiment.base_exp_id);
        end if;
    end if;

    -- Get the base experiment id from a named experiment.
    if _base_exp_id is null and base_experiment is not null then
        _base_exp_project_id = _project_id;
        select
            experiments.id into _base_exp_id
        from
            experiments join projects on experiments.project_id = projects.id
        where
            experiments.name = base_experiment
            and experiments.project_id = _project_id
            and experiments.deleted_at isnull
            and projects.deleted_at isnull
        ;
        if not found then
            raise exception '%', jsonb_build_object('kind', 'http-error', 'code', 400, 'message', format('No base experiment found with name %s', base_experiment));
        end if;
    end if;

    -- If still no base experiment, pick the latest experiment of the first
    -- ancestor which has any experiments as the base and the user has read
    -- permissions for.
    if _base_exp_id is null and ancestor_commits is not null then
        _base_exp_project_id = _project_id;
        _found_base_exp_id_from_ancestor_commits = true;
        select
            id into _base_exp_id
        from (
            select
                c.nr as commit_nr,
                experiments.id,
                experiments.created
            from
                unnest(ancestor_commits) with ordinality c(commit, nr)
                join experiments
                    on experiments.commit = c.commit
                    and experiments.project_id = _project_id
                join projects on experiments.project_id = projects.id
            where
                has_under_project_acl('experiment', experiments.id,
                                      _project_id, 'read', _user_id)
                and experiments.deleted_at isnull
                and projects.deleted_at isnull
            order by
                commit_nr asc, created desc
            limit 1
        ) sub
        ;
    end if;

    -- Permissions check for base experiment (we can skip when deriving from an
    -- ancestor commit, because we already did the check there).
    if _base_exp_id is not null and not _found_base_exp_id_from_ancestor_commits then
        if not has_under_project_acl('experiment', _base_exp_id,
                                     _base_exp_project_id, 'read',
                                     _user_id) then
            raise exception '%', jsonb_build_object('kind', 'not-found', 'permission', 'read', 'objectType', 'experiment', 'objectId', _base_exp_id);
        end if;
    end if;

    -- Search for an existing experiment matching the provided name.
    if experiment_name is not null then
        select
            experiments.id into _conflicting_experiment_id
        from
            experiments join projects on experiments.project_id = projects.id
        where
            experiments.project_id = _project_id
            and experiments.name = register_experiment.experiment_name
            and experiments.deleted_at isnull
            and projects.deleted_at isnull
        ;
    end if;

    -- If we have a conflicting experiment and are updating, just return the
    -- existing experiment.
    if _conflicting_experiment_id is not null and _update then
        if not has_under_project_acl('experiment', _conflicting_experiment_id,
                                     _project_id, 'read', _user_id) then
            raise exception '%', jsonb_build_object('kind', 'not-found', 'permission', 'read', 'objectType', 'experiment', 'objectId', _conflicting_experiment_id);
        end if;
        _inserted_experiment_id := _conflicting_experiment_id;
    else
        -- If no experiment_name was provided, we generate a default name of the
        -- form (<branch> | <user_email>)-<unix timestamp>. Otherwise, we start
        -- with the provided experiment_name.
        --
        -- If there is already an experiment with the potential name, we append
        -- a portion of a UUID to guarantee uniqueness.
        declare
            _insert_experiment_name text;
        begin
            if experiment_name is null then
                _insert_experiment_name := concat_ws(
                    '-',
                    coalesce(_repo_info->>'branch', get_user_email(_user_id)),
                    extract('epoch' from now())::bigint);
            else
                _insert_experiment_name := experiment_name;
            end if;

            select insert_experiment(
                _user_id, _project_id, _insert_experiment_name, description,
                _repo_info, _base_exp_id, dataset_id, dataset_version,
                metadata, tags)
            into _inserted_experiment_id;

            if _inserted_experiment_id is null then
                _insert_experiment_name := concat_ws(
                    '-', _insert_experiment_name,
                    substring(gen_random_uuid()::text for 8));
                select insert_experiment(
                    _user_id, _project_id, _insert_experiment_name, description,
                    _repo_info, _base_exp_id, dataset_id, dataset_version,
                    metadata, tags)
                into _inserted_experiment_id;
            end if;

            perform register_acl_unchecked(
                _object_type => 'experiment', _object_id => _inserted_experiment_id,
                _user_id => _user_id, _role_id => get_owner_role_id());

            if _public then
                perform mark_experiment_as_public(
                    experiment_id => _inserted_experiment_id,
                    project_id => _project_id,
                    performing_user_id => _user_id);
            else
                perform mark_experiment_as_nonpublic(
                    experiment_id => _inserted_experiment_id,
                    project_id => _project_id,
                    performing_user_id => _user_id);
            end if;
        end;
    end if;

    -- The additional projections are copied from
    -- app/pages/api/experiment/_constants.ts.
    return (
        select jsonb_build_object('project', projects, 'experiment', experiments)
        from (
            select
                *,
                has_under_project_acl('experiment', experiments.id, experiments.project_id, 'read', anon_user_id()) as "public"
            from
                experiments
            where
                experiments.id = _inserted_experiment_id
        ) experiments join projects on projects.id = experiments.project_id
    );
end;
$$;

revoke execute on function register_experiment from public, anon;

-- Define prompt_sessions.

create table prompt_sessions (
    id uuid not null primary key default uuid_generate_v4(),
    name text not null,
    description text,
    created timestamp with time zone default current_timestamp,
    deleted_at timestamp with time zone,
    user_id uuid references users,
    project_id uuid not null references projects, -- This is at the end because it was ALTERed in
    org_id uuid -- Ugh, for back-compat reasons we need to include this. DEPRECATION_NOTICE once people are on 0.0.38, we can remove
);

create index on prompt_sessions (project_id);
create unique index on prompt_sessions (project_id, name, deleted_at) nulls not distinct;

alter table prompt_sessions enable row level security;

create function insert_prompt_session(
    user_id uuid,
    project_id uuid,
    name text,
    description text)
returns uuid
language plpgsql
security definer
as $$
declare
    _prompt_session_id uuid;
begin
    if not has_project_acl('prompt_session', project_id, 'create', user_id) then
        raise exception '%', jsonb_build_object('kind', 'not-found', 'permission', 'create prompt_session', 'objectType', 'project', 'objectId', project_id);
    end if;

    insert into prompt_sessions(project_id, name, description, user_id)
    values (project_id, name, description, user_id)
    on conflict do nothing
    returning id into _prompt_session_id
    ;

    return _prompt_session_id;
end;
$$;

revoke execute on function insert_prompt_session from public, anon;

create or replace function register_prompt_session(
    auth_id uuid,
    org_name text,
    project_name text,
    session_name text)
returns jsonb
language plpgsql
security definer
as $$
declare
  _user_id uuid = get_user_id_by_auth_id(auth_id);
  _org_id uuid;
  _project_id uuid;
  _prompt_session_id uuid;
begin
    select
        id into _org_id
    from
        organizations
    where
        organizations.name = org_name
    ;
    if not found then
        raise exception '%', jsonb_build_object('kind', 'http-error', 'code', 400, 'message', format('No organization with name %s', org_name));
    end if;


    _project_id = get_or_create_project(auth_id, project_name, _org_id);

    select insert_prompt_session(
            user_id => _user_id,
            project_id => _project_id,
            name => session_name,
            description => null)
    into _prompt_session_id;

    if _prompt_session_id is null then
        -- Duplicate key.
        select prompt_sessions.id
        from prompt_sessions join projects on prompt_sessions.project_id = projects.id
        where
            project_id = _project_id
            and prompt_sessions.name = session_name
            and prompt_sessions.deleted_at isnull
            and projects.deleted_at isnull
        into _prompt_session_id
        ;

        if not found then
            raise exception 'Prompt session % (under org %) likely deleted concurrently with creation',
                session_name, _org_id;
        end if;
    else
        perform register_acl_unchecked(
            _object_type => 'prompt_session', _object_id => _prompt_session_id,
            _user_id => _user_id, _role_id => get_owner_role_id());
    end if;

    return (
        select to_jsonb(prompt_sessions)
        from prompt_sessions
        where prompt_sessions.id = _prompt_session_id
    );
end;
$$;

revoke execute on function register_prompt_session from public, anon;

create table prompts (
    id uuid not null primary key default uuid_generate_v4(),
    project_id uuid not null references projects,
    slug text not null,
    created timestamp with time zone default current_timestamp,
    deleted_at timestamp with time zone,
    user_id uuid references users
);
alter table prompts enable row level security;

create unique index on prompts (project_id, slug, deleted_at) nulls not distinct;

create function register_prompt(
    auth_id uuid,
    org_id uuid,
    project_id uuid default null,
    project_name text default null,
    slug text default null,
    update boolean default null,
    prompt_id uuid default null)
returns jsonb
language plpgsql
security definer
as $$
declare
    -- Initialize meaningful defaults.
    _update boolean := coalesce(update, false);
    _prompt_id uuid := coalesce(prompt_id, uuid_generate_v4());

    _user_id uuid := get_user_id_by_auth_id(auth_id);
    _project_id uuid;
    _inserted_prompt_id uuid;
    _found_existing boolean := false;
begin
    -- Assign the project_id.
    if register_prompt.project_id is not null then
        -- Check that the project exists.
        select projects.id into _project_id
        from projects
        where
            projects.id = register_prompt.project_id
            and projects.deleted_at isnull
        ;
        if not found then
            raise exception '%', jsonb_build_object('kind', 'not-found', 'permission', 'read', 'objectType', 'project', 'objectId', register_prompt.project_id);
        end if;
    else
        _project_id = get_or_create_project(auth_id, project_name, org_id);
    end if;

    if not has_project_acl('prompt', _project_id, 'create', _user_id) then
        raise exception '%', jsonb_build_object('kind', 'not-found', 'permission', 'create prompt', 'objectType', 'project', 'objectId', _project_id);
    end if;

    insert into prompts (id, project_id, user_id, slug)
    values (_prompt_id, _project_id, _user_id, register_prompt.slug)
    on conflict do nothing
    returning id into _inserted_prompt_id
    ;

    if _inserted_prompt_id is null then
        -- Duplicate key.
        _found_existing := true;
        select prompts.id
        from
            prompts
            join projects on prompts.project_id = projects.id
        where
            prompts.project_id = _project_id
            and prompts.slug = register_prompt.slug
            and prompts.deleted_at isnull
            and projects.deleted_at isnull
        into _prompt_id
        ;
        if not found then
            raise exception 'Prompt % (under project %) likely deleted concurrently with creation',
                register_prompt.slug, _project_id;
        end if;

        -- Update is just ignored for now
    else
        perform register_acl_unchecked(
            _object_type => 'prompt', _object_id => _prompt_id,
            _user_id => _user_id, _role_id => get_owner_role_id());
    end if;

    return (
        select jsonb_build_object('project', projects, 'prompt', prompts, 'found_existing', _found_existing)
        from projects join prompts on projects.id = prompts.project_id
        where prompts.id = _prompt_id
    );
end;
$$;

revoke execute on function register_prompt from public, anon;

-- Define user_feedback, for collecting feedbock

create table user_feedback (
    id uuid not null primary key default gen_random_uuid(),
    user_id uuid references users,
    created timestamp with time zone default current_timestamp,
    content text,
    emoji text,
    page text
);

alter table user_feedback enable row level security;

-- Broadcast changes to relevant tables to the supabase_realtime channel
-- (https://supabase.com/docs/guides/realtime).
create publication supabase_realtime with (publish = 'insert, update, delete, truncate');
alter publication supabase_realtime add table experiments;
alter publication supabase_realtime add table projects;


create type score_type as enum ('slider', 'categorical', 'weighted', 'minimum', 'maximum', 'online', 'free-form');
create table project_scores (
    id uuid not null primary key default uuid_generate_v4(),
    project_id uuid not null references projects(id),
    user_id uuid not null references users(id),
    created timestamp with time zone default current_timestamp,

    -- The score itself
    name text not null,
    description text,
    score_type score_type NOT NULL,
    categories jsonb,
    config jsonb,
    -- The position column will store Lexorank strings for sorting project scores in the UI
    position text
);

create unique index project_scores_project_id_name_idx on project_scores(project_id, name);

-- Let's see if we can implement everything needed for project scores without granular RLS.
-- So just create a generic lockdown rule
alter table project_scores enable row level security;


create table project_tags (
    id uuid not null primary key default uuid_generate_v4(),
    project_id uuid not null references projects(id),
    user_id uuid not null references users(id),
    created timestamp with time zone default current_timestamp,

    -- The score itself
    name text not null,
    description text,
    color text,

    -- Ordering
    position text
);

create unique index project_tags_project_id_name_idx on project_tags(project_id, name);
alter table project_tags enable row level security;

create table span_iframes (
    id uuid not null primary key default uuid_generate_v4(),
    project_id uuid not null references projects(id),
    user_id uuid not null references users(id),
    name text not null,
    description text,
    url text not null,
    post_message boolean not null default false,
    created timestamp with time zone default current_timestamp,
    deleted_at timestamp with time zone
);

create unique index span_iframes_custom_id_name_idx on span_iframes(project_id, name);
alter table span_iframes enable row level security;

-- BEGIN SECTION RBAC SCHEMA

-- Type and table definitions

-- Groups are collections of users, which may be assigned their own access
-- controls. Groups can consist of users as well as other groups.

create table groups(
    id uuid not null primary key default uuid_generate_v4(),
    -- It is forbidden to change the org after creating a group.
    org_id uuid not null references organizations,
    user_id uuid references users,
    created timestamp with time zone default current_timestamp,
    name text not null,
    description text,
    deleted_at timestamp with time zone
);
alter table groups enable row level security;
create unique index on groups(org_id, name, deleted_at) nulls not distinct;
-- For foreign keys.
create unique index on groups(id, org_id);

create table group_users(
    group_id uuid not null,
    user_id uuid not null,
    -- This is derived and set by an insert/update trigger.
    _group_org_id uuid not null,
    primary key (group_id, user_id),
    foreign key (group_id, _group_org_id) references groups(id, org_id),
    foreign key (_group_org_id, user_id) references members(org_id, user_id)
);
alter table group_users enable row level security;

create table group_members(
    group_id uuid not null references groups,
    member_group_id uuid not null references groups,
    primary key (group_id, member_group_id)
);
alter table group_members enable row level security;
-- For querying the reverse relationship.
create index on group_members(member_group_id, group_id);

-- A permission is a verb permitting a certain type of operation on a specific
-- object in the system. They can be assigned on an individual basis, or grouped
-- into roles.
create type permission_type as enum (
    -- CRUD operations on the object itself.
    'create',
    'read',
    'update',
    'delete',
    -- CRUD operations on the object ACLs.
    'create_acls',
    'read_acls',
    'update_acls',
    'delete_acls'
);

-- The set of object type "nouns" that an ACL can apply to. These are used both
-- for specifying "where in the hierarchy" an ACL lives (e.g. org, project, or
-- experiment-level), and also "which object" an ACL applies to (e.g. only allow
-- reads on experiments, or allow reads on all sub-objects).
--
-- Note: All nouns marked deprecated should not be present in the ACLs tables.
-- They can be removed by replacing the `acl_object_type` enum with one that
-- does not include them (a non-trivial but doable migration).
create type acl_object_type as enum (
    -- These nouns correspond to exact tables.
    'organization',
    'project',
    'experiment',
    'dataset',
    'prompt',
    'prompt_session',
    'project_score', -- deprecated.
    'project_tag', -- deprecated.
    'group',
    'role',
    -- org_member is a "virtual" noun, denoting an organization-level permission
    -- to control which users are members of the org, i.e. permission to
    -- invite/remove users.
    'org_member',
    -- project_log is a "virtual" noun , denoting a project-level permission
    -- over the logs. It is virtual because currently there is only one log
    -- stream per project, and the ID is the same as the project.
    'project_log',
    -- org_project is a "virtual" noun, denoting an organization-level
    -- permission over all projects in the org. It enables people to assign
    -- permissions to all projects in the org (and any of their sub-objects such
    -- as experiments) without assigning permissions to other org-level
    -- siblings, such as groups and org_members.
    'org_project'
);

create table roles(
    id uuid not null primary key default uuid_generate_v4(),
    -- A null org_id indicates a system role, which may be assigned to anybody
    -- and inherited by any other role, but only edited by the DB superuser.
    -- System roles also may not inherit from non-system roles. A role with an
    -- org_id is called a custom role.
    --
    -- It is forbidden to change the org after creating a role.
    org_id uuid references organizations,
    -- Similarly, system roles do not have user_ids.
    user_id uuid references users,
    created timestamp with time zone default current_timestamp,
    name text not null,
    description text,
    deleted_at timestamp with time zone
);
alter table roles enable row level security;
create unique index on roles(org_id, name, deleted_at) nulls not distinct;
-- For foreign keys.
create unique index on roles(id, org_id);

create table role_permissions(
    role_id uuid not null references roles,
    permission permission_type not null,
    restrict_object_type acl_object_type
);
alter table role_permissions enable row level security;
-- Allows deleting from the role permissions table even though it doesn't have a
-- primary key.
alter table role_permissions replica identity full;

create unique index on role_permissions(role_id, permission, restrict_object_type) nulls not distinct;

create table role_members(
    role_id uuid not null references roles,
    member_role_id uuid not null references roles,
    primary key (role_id, member_role_id)
);
alter table role_members enable row level security;
-- For querying the reverse relationship.
create index on role_members(member_role_id, role_id);

-- Each ACL attaches to a specific object in the hierarchy, named by
-- (object_type, object_id), a user/group, named by (user_object_type, user_id,
-- group_id), and a grant/role, named by (grant_object_type, permission,
-- role_id).
--
-- The ACL will grant every member of the group the set of permissions in the
-- role for the specified object AND any object beneath it in the object
-- hierarchy. E.g. granting 'read' permission on an organization will imply read
-- permission on all experiments, datasets, etc. within the org.
--
-- The ACL may optionally specify 'restrict_object_type' to restrict the
-- permission to just sub-objects of a certain type. For example, granting
-- 'read' permission on an organization with 'restrict_object_type = dataset'
-- will imply read permission on just datasets within the org. A null value for
-- 'restrict_object_type' means the ACL applies to all object types. Note that
-- roles are a set of (permission, restrict_object_type), so an ACL tuple can
-- either have a single (permission, restrict_object_type) or a role_id.

create type acl_user_object_type as enum ('user', 'group');

create type acl_grant_object_type as enum ('permission', 'role');

create table acls(
    id uuid not null primary key default uuid_generate_v4(),
    object_type acl_object_type not null,
    object_id uuid not null,
    user_object_type acl_user_object_type not null,
    -- not null if user_object_type = 'user'
    user_id uuid,
    -- not null if user_object_type = 'group'
    group_id uuid,
    grant_object_type acl_grant_object_type not null,
    -- not null if grant_object_type = 'permission'
    permission permission_type,
    -- not null if grant_object_type = 'role'
    role_id uuid,
    -- Optionally restrict the ACL to a specific type of sub-object. Null means
    -- no restriction. Can only be specified if 'grant_object_type =
    -- permission'.
    restrict_object_type acl_object_type,
    -- This is derived and set by an insert/update trigger. If provided
    -- explicitly, it must agree with the org ID derived from (object_type,
    -- object_id).
    _object_org_id uuid not null,
    created timestamp with time zone default current_timestamp,
    -- We want to enforce that the user is part of the same org as the ACL
    -- object, except for the special-case of the anonymous user.
    _foreign_key_user_id uuid generated always as (nullif(user_id, anon_user_id())) stored,
    foreign key (_object_org_id, _foreign_key_user_id) references members(org_id, user_id),
    foreign key (group_id, _object_org_id) references groups(id, org_id),
    -- While it would be nice to have a foreign key on (role_id, _object_org_id)
    -- to roles(id, org_id), this would prevent us from assigning a system role
    -- to some organization object, because system roles have no org_id. Thus we
    -- must enforce this with more complicated logic in the trigger. We should
    -- be protected against changing the org of a role by the `update_roles`
    -- trigger.
    foreign key (role_id) references roles(id)
);
alter table acls enable row level security;

create unique index on acls(
    object_type, object_id, user_object_type, user_id, group_id,
    grant_object_type, permission, role_id, restrict_object_type) nulls not distinct;
-- For quickly identifying which org an ACL belongs to.
create index on acls(_object_org_id);
-- For quickly identifying which group/role an ACL uses.
create index on acls(group_id);
create index on acls(role_id);

-- We strictly serialize any statements which modify RBAC tables, because
-- updating the materialized views requires reading and writing several tables
-- in one transaction. In general it's not easy to reason granularly about which
-- row locks any modification will require, so we just acquire a single advisory
-- lock before any statement which modifies any RBAC table.

create function acquire_acl_lock()
returns void
language sql
security invoker
as $$
    -- This must be kept in sync with app/utils/advisory-locks.ts.
    select pg_advisory_xact_lock(0);
$$;

revoke execute on function acquire_acl_lock from public, anon;

create function acquire_acl_lock_trigger_f()
returns trigger
language plpgsql
security invoker
as $$
begin
    perform acquire_acl_lock();
    return null;
end;
$$;

revoke execute on function acquire_acl_lock_trigger_f from public, anon;

create trigger acquire_acl_lock_groups_trigger
    before insert or update or delete on groups
    for each statement execute procedure acquire_acl_lock_trigger_f();
create trigger acquire_acl_lock_group_users_trigger
    before insert or update or delete on group_users
    for each statement execute procedure acquire_acl_lock_trigger_f();
create trigger acquire_acl_lock_group_members_trigger
    before insert or update or delete on group_members
    for each statement execute procedure acquire_acl_lock_trigger_f();
create trigger acquire_acl_lock_roles_trigger
    before insert or update or delete on roles
    for each statement execute procedure acquire_acl_lock_trigger_f();
create trigger acquire_acl_lock_role_permissions_trigger
    before insert or update or delete on role_permissions
    for each statement execute procedure acquire_acl_lock_trigger_f();
create trigger acquire_acl_lock_role_members_trigger
    before insert or update or delete on role_members
    for each statement execute procedure acquire_acl_lock_trigger_f();
create trigger acquire_acl_lock_acls_trigger
    before insert or update or delete on acls
    for each statement execute procedure acquire_acl_lock_trigger_f();

-- Triggers for enforcing constraints and filling in derived information on the
-- RBAC tables. These are applied before each row.

create function check_update_group()
returns trigger
language plpgsql
security invoker
as $$
begin
    if old.org_id is distinct from new.org_id then
        raise exception 'Cannot modify org_id of group';
    end if;
    return new;
end;
$$;

revoke execute on function check_update_group from public, anon;

create trigger update_groups_trigger
    before update on groups
    for each row execute procedure check_update_group();

create function process_new_group_user()
returns trigger
language plpgsql
security invoker
as $$
begin
    select groups.org_id into new._group_org_id
    from groups where groups.id = new.group_id;
    return new;
end;
$$;

revoke execute on function process_new_group_user from public, anon;

create trigger upsert_group_users_trigger
    before insert or update on group_users
    for each row execute procedure process_new_group_user();

create function check_new_group_member()
returns trigger
language plpgsql
security invoker
as $$
declare
    _group_org_id uuid;
    _member_group_org_id uuid;
begin
    select groups.org_id into _group_org_id
    from groups where groups.id = new.group_id;
    select groups.org_id into _member_group_org_id
    from groups where groups.id = new.member_group_id;

    if _group_org_id <> _member_group_org_id then
        raise exception 'Both groups must belong to the same organization';
    end if;

    return new;
end;
$$;

revoke execute on function check_new_group_member from public, anon;

create trigger upsert_group_members_trigger
    before insert or update on group_members
    for each row execute procedure check_new_group_member();

create function check_update_role()
returns trigger
language plpgsql
security invoker
as $$
begin
    if old.org_id is distinct from new.org_id then
        raise exception 'Cannot modify org_id of role';
    end if;
    return new;
end;
$$;

revoke execute on function check_update_role from public, anon;

create trigger update_roles_trigger
    before update on roles
    for each row execute procedure check_update_role();

create function check_new_role_member()
returns trigger
language plpgsql
security invoker
as $$
declare
    _role_org_id uuid;
    _member_role_org_id uuid;
begin
    select roles.org_id into _role_org_id
    from roles where roles.id = new.role_id;
    select roles.org_id into _member_role_org_id
    from roles where roles.id = new.member_role_id;

    if (_role_org_id is not null and _member_role_org_id is not null and _role_org_id <> _member_role_org_id) then
        raise exception 'Both roles must belong to the same organization';
    end if;

    if (_role_org_id is null and _member_role_org_id is not null) then
        raise exception 'System roles cannot contain non-system roles';
    end if;

    return new;
end;
$$;

revoke execute on function check_new_role_member from public, anon;

create trigger upsert_role_members_trigger
    before insert or update on role_members
    for each row execute procedure check_new_role_member();

-- Return the org id that the given _object_id falls under. If an org id is not
-- found, throws an exception if the _object_type specifies an object where this
-- is derivable (most objects), otherwise returns null.
create function get_new_acl_object_org_id(
    _object_type acl_object_type, _object_id uuid)
returns uuid
language plpgsql
security invoker
stable
as $$
declare
    _object_org_id uuid;
begin
    if (_object_type = 'organization' or _object_type = 'org_member' or _object_type = 'org_project') then
        _object_org_id = _object_id;
    elsif (_object_type = 'project' or _object_type = 'project_log') then
        select projects.org_id into _object_org_id
        from projects where projects.id = _object_id;
    elsif _object_type = 'experiment' then
        select projects.org_id into _object_org_id
        from projects join experiments on projects.id = experiments.project_id
        where experiments.id = _object_id;
    elsif _object_type = 'dataset' then
        select projects.org_id into _object_org_id
        from projects join datasets on projects.id = datasets.project_id
        where datasets.id = _object_id;
    elsif _object_type = 'prompt' then
        select projects.org_id into _object_org_id
        from projects join prompts on projects.id = prompts.project_id
        where prompts.id = _object_id;
    elsif _object_type = 'prompt_session' then
        select projects.org_id into _object_org_id
        from projects join prompt_sessions on projects.id = prompt_sessions.project_id
        where prompt_sessions.id = _object_id;
    elsif _object_type = 'group' then
        select groups.org_id into _object_org_id
        from groups where groups.id = _object_id;
    elsif _object_type = 'role' then
        select roles.org_id into _object_org_id
        from roles where roles.id = _object_id;
        if _object_org_id is null then
            raise exception 'Cannot create ACL on system role object';
        end if;
    end if;
    if _object_org_id is null then
        raise exception 'ACL object id is invalid for % object', _object_type;
    end if;
    return _object_org_id;
end;
$$;

revoke execute on function get_new_acl_object_org_id from public, anon;

create function process_new_acl()
returns trigger
language plpgsql
security invoker
as $$
declare
    _object_org_id uuid;
begin
    -- If _object_org_id is null, the user must have supplied one in the new
    -- row. If it is non-null and the user has supplied one anyways, they must
    -- agree.
    _object_org_id = get_new_acl_object_org_id(new.object_type, new.object_id);
    if _object_org_id is null then
        if new._object_org_id is null then
            raise exception 'Must supply _object_org_id explicitly for non-derivable ACL object %', new.object_type;
        else
            _object_org_id = new._object_org_id;
        end if;
    else
        if new._object_org_id is null then
            new._object_org_id = _object_org_id;
        elsif _object_org_id <> new._object_org_id then
            raise exception 'Derived org id does not match provided org id for % object', new.object_type;
        end if;
    end if;
    if _object_org_id is null or new._object_org_id is null then
        raise exception 'Impossible';
    end if;

    -- Check that the field type enum corresponds to the correct field, and that
    -- exactly one field in each category is set.

    if ((new.user_object_type = 'user' and new.user_id is null) or
        (new.user_object_type = 'group' and new.group_id is null)) then
        raise exception 'user_object_type must correspond to set column';
    end if;
    if ((new.user_id is not null)::int + (new.group_id is not null)::int <> 1) then
        raise exception 'At most one user object column must be set';
    end if;

    if ((new.grant_object_type = 'permission' and new.permission is null) or
        (new.grant_object_type = 'role' and new.role_id is null)) then
        raise exception 'grant_object_type must correspond to set column';
    end if;
    if ((new.permission is not null)::int + (new.role_id is not null)::int <> 1) then
        raise exception 'At most one grant object column must be set';
    end if;
    if (new.restrict_object_type is not null and new.role_id is not null) then
        raise exception 'Cannot set both role_id and restrict_object_type';
    end if;

    -- If the role corresponds to a non-system role, its org must match
    -- _object_org_id.
    if new.grant_object_type = 'role' then
        declare
            _role_org_id uuid;
        begin
            select roles.org_id into _role_org_id
            from roles where roles.id = new.role_id;
            if _role_org_id is not null and _role_org_id <> _object_org_id then
                raise exception 'Custom role must belong to same org as object';
            end if;
        end;
    end if;

    return new;
end;
$$;

revoke execute on function process_new_acl from public, anon;

create trigger upsert_acls_trigger
    before insert or update on acls
    for each row execute procedure process_new_acl();

-- In addition to the ground-truth tables, we define "expanded" tables, which
-- reduce the nested information in the ground-truth tables to a more basic form
-- that is efficient to query.

-- An expanded materialization of the group_users and group_members tables
-- which walks through the group inheritance hierarchy and allows quickly
-- checking whether a user or group inherits any grants from a parent group.
-- Note that the table includes the tautological member (group_id_X, 'group',
-- group_id_X) for every group to make it easy to join in the full set of groups
-- that obtain a particular ACL.
create table _expanded_group_members(
    group_id uuid not null,
    user_object_type acl_user_object_type not null,
    user_group_id uuid not null,
    _group_org_id uuid not null,
    primary key (group_id, user_object_type, user_group_id)
);
alter table _expanded_group_members enable row level security;
-- For quickly identifying which org a group-member entry belongs to.
create index on _expanded_group_members(_group_org_id);
-- For quickly identifying which groups a particular user/group is a member of.
create index on _expanded_group_members(user_object_type, user_group_id, group_id);

-- Analogue of _expanded_group_members for roles.
create table _expanded_role_permissions(
    role_id uuid not null,
    permission permission_type,
    _role_org_id uuid,
    restrict_object_type acl_object_type,
    grant_object_type acl_grant_object_type not null,
    member_role_id uuid
);
alter table _expanded_role_permissions enable row level security;

create unique index on _expanded_role_permissions(role_id, grant_object_type, permission, restrict_object_type, member_role_id) nulls not distinct;

-- For quickly identifying which org a role grant belongs to.
create index on _expanded_role_permissions(_role_org_id);
-- For quickly identifying which roles a particular role/permission is a member of.
create index on _expanded_role_permissions(grant_object_type, permission, restrict_object_type, member_role_id, role_id);

-- An expanded materialization of the acls table, which joins in both
-- expanded_group_members and expanded_role_permissions to allow quickly
-- querying whether a particular (object, user, grant) tuple exists.
create table _expanded_acls(
    acl_id uuid not null,
    object_type acl_object_type not null,
    object_id uuid not null,
    user_object_type acl_user_object_type not null,
    user_group_id uuid not null,
    permission permission_type not null,
    restrict_object_type acl_object_type,
    _object_org_id uuid not null
);
alter table _expanded_acls enable row level security;
-- We allow inserting the same "expanded" ACL from multiple "base" ACLs, so that
-- we can query which base ACLs are responsible for an expanded ACL.
create unique index on _expanded_acls(acl_id, object_type, object_id, user_object_type, user_group_id, permission, restrict_object_type) nulls not distinct;
-- But we still generally want to search for ACLs by their logical content.
create index on _expanded_acls(object_type, object_id, user_object_type, user_group_id, permission, restrict_object_type);
-- For quickly identifying which org an ACL belongs to.
create index on _expanded_acls(_object_org_id);

-- Functions for maintaining incrementally updated materialized views. These are
-- defined in reverse order of dependency, because updates to the more basic
-- views will need to update the more derived ones.

create function refresh_expanded_acls_by_object(
    _object_type acl_object_type, _object_id uuid)
-- The return value is an arbitrary int to ensure the optimizer runs all parts
-- of the query we specify.
returns integer
language plpgsql
security invoker
as $$
declare
    _out integer;
begin
    -- Acquire locks against other RBAC operations.
    perform acquire_acl_lock();

    -- Query generated from the following command:
    --
    -- ./scripts/make_expanded_acls_query.py "(_object_type isnull and _object_id isnull) or (object_type = _object_type and object_id = _object_id)"
    with
    candidate_acls as (
        select *
        from acls
        where ((_object_type isnull and _object_id isnull) or (object_type = _object_type and object_id = _object_id))
    ),
    joined_acls as (
        select
            candidate_acls.*,
            _expanded_group_members.user_object_type expanded_user_object_type,
            _expanded_group_members.user_group_id expanded_user_group_id,
            _expanded_role_permissions.permission expanded_permission,
            _expanded_role_permissions.restrict_object_type expanded_restrict_object_type
        from
            candidate_acls
                left join _expanded_group_members using (group_id)
                left join _expanded_role_permissions using (role_id)
        where
            _expanded_role_permissions.role_id is null or _expanded_role_permissions.grant_object_type = 'permission'
    ),
    coalesced_acls as (
    select
        id acl_id,
        object_type,
        object_id,
        coalesce(expanded_user_object_type, user_object_type) as user_object_type,
        coalesce(expanded_user_group_id, user_id) as user_group_id,
        coalesce(expanded_permission, permission) as permission,
        coalesce(expanded_restrict_object_type, restrict_object_type) as restrict_object_type,
        _object_org_id
    from
        joined_acls
    ),
    final_acls as (
        select * from coalesced_acls
        where
            -- It is possible that the user specifies an empty group or role, in
            -- which case we don't need to include these entries in the expanded
            -- ACLs.
            user_object_type is not null
            and user_group_id is not null
            and permission is not null
    ),
    deleted_acls as (
        delete from _expanded_acls where
        ((_object_type isnull and _object_id isnull) or (object_type = _object_type and object_id = _object_id))
        and not exists(
            select 1 from final_acls
            where
                _expanded_acls.acl_id = final_acls.acl_id
                and _expanded_acls.object_type = final_acls.object_type
                and _expanded_acls.object_id = final_acls.object_id
                and _expanded_acls.user_object_type = final_acls.user_object_type
                and _expanded_acls.user_group_id = final_acls.user_group_id
                and _expanded_acls.permission = final_acls.permission
                and ((_expanded_acls.restrict_object_type isnull and final_acls.restrict_object_type isnull)
                     or (_expanded_acls.restrict_object_type = final_acls.restrict_object_type))
        )
        returning 1
    ),
    inserted_acls as (
        insert into _expanded_acls select * from final_acls on conflict do nothing
        returning 1
    )
    select num_deleted.cnt + num_inserted.cnt into _out
    from
        (select count(*) cnt from deleted_acls) "num_deleted"
        join (select count(*) cnt from inserted_acls) "num_inserted"
        on true;
    return _out;
end;
$$;

revoke execute on function refresh_expanded_acls_by_object from public, anon;

create function refresh_expanded_group_members_by_group(_arg_group_id uuid)
-- The return value is an arbitrary int to ensure the optimizer runs all parts
-- of the query we specify.
returns integer
language plpgsql
security invoker
as $$
declare
    _out0 integer;
    _out1 integer;
begin
    -- Acquire locks against other RBAC operations.
    perform acquire_acl_lock();

    -- Rebuild the set of group members, either across the entire table, or
    -- scoped to the groups which '_arg_group_id' is a member of.
    with recursive
    groups_to_update as (
        select group_id from _expanded_group_members
        where (_arg_group_id is null or user_group_id = _arg_group_id) and user_object_type = 'group'
        union all
        select _arg_group_id group_id where _arg_group_id is not null
    ),
    t(group_id, user_object_type, user_group_id, _group_org_id) as (
        (
            select groups.id, 'group'::acl_user_object_type, groups.id, groups.org_id
            from groups join groups_to_update on groups_to_update.group_id = groups.id
            where groups.deleted_at is null
        )
        union
        select * from (
            -- Postgres is weird and doesn't allow the recursive term to appear
            -- more than once, but it is just a parser thing.
            with t_inner as (select * from t)
            select t_inner.group_id, 'user'::acl_user_object_type, group_users.user_id, t_inner._group_org_id
            from t_inner join group_users on t_inner.user_group_id = group_users.group_id
            where t_inner.user_object_type = 'group'
            union all
            select t_inner.group_id, 'group'::acl_user_object_type, group_members.member_group_id, t_inner._group_org_id
            from
                t_inner
                join group_members on t_inner.user_group_id = group_members.group_id
                join groups on group_members.member_group_id = groups.id
            where t_inner.user_object_type = 'group' and groups.deleted_at is null
        ) "x"
    ),
    final_result_set as (
        select * from t
    ),
    deleted_members as (
        delete from _expanded_group_members where
        _expanded_group_members.group_id in (select group_id from groups_to_update)
        and not exists (
            select 1 from final_result_set
            where
                _expanded_group_members.group_id = final_result_set.group_id
                and _expanded_group_members.user_object_type = final_result_set.user_object_type
                and _expanded_group_members.user_group_id = final_result_set.user_group_id
        )
        returning 1
    ),
    inserted_members as (
        insert into _expanded_group_members select * from final_result_set on conflict do nothing
        returning 1
    )
    select num_deleted.cnt + num_inserted.cnt into _out0
    from
        (select count(*) cnt from deleted_members) "num_deleted"
        join (select count(*) cnt from inserted_members) "num_inserted"
        on true;

    -- Rebuild the set of expanded ACLs for all objects which have an ACL on
    -- this group or any group which inherits from it.
    with
    acl_groups_to_update as (
        select user_group_id group_id from _expanded_group_members
        where (_arg_group_id is null or group_id = _arg_group_id) and user_object_type = 'group'
        -- Note that we still need to explicitly include the input group in case
        -- it was deleted, so it's no longer present in _expanded_group_members,
        -- but we still need to update the ACLs which reference it.
        union all
        select _arg_group_id group_id where _arg_group_id is not null
    ),
    acl_objects_to_update as (
        select distinct acls.object_type, acls.object_id
        from acls join acl_groups_to_update on acls.group_id = acl_groups_to_update.group_id
    ),
    updated_acls as (
        select refresh_expanded_acls_by_object(object_type, object_id) ret
        from acl_objects_to_update
    )
    select sum(ret) into _out1 from updated_acls;

    return _out0 + _out1;
end;
$$;

revoke execute on function refresh_expanded_group_members_by_group from public, anon;

create function refresh_expanded_role_permissions_by_role(_arg_role_id uuid)
returns integer
language plpgsql
security invoker
as $$
declare
    _out0 integer;
    _out1 integer;
begin
    -- Acquire locks against other RBAC operations.
    perform acquire_acl_lock();

    -- Rebuild the set of role grants, either across the entire table, or scoped
    -- to the roles which '_arg_role_id' is a member of.
    with recursive
    roles_to_update as (
        select role_id from _expanded_role_permissions
        where (_arg_role_id is null or (permission is null and restrict_object_type is null and member_role_id = _arg_role_id)) and grant_object_type = 'role'
        union all
        select _arg_role_id role_id where _arg_role_id is not null
    ),
    t(role_id, permission, _role_org_id, restrict_object_type, grant_object_type, member_role_id) as (
        (
            select roles.id, null::permission_type, roles.org_id, null::acl_object_type, 'role'::acl_grant_object_type, roles.id
            from roles join roles_to_update on roles_to_update.role_id = roles.id
            where roles.deleted_at is null
        )
        union
        select * from (
            -- Postgres is weird and doesn't allow the recursive term to appear
            -- more than once, but it is just a parser thing.
            with t_inner as (select * from t)
            select t_inner.role_id, role_permissions.permission, t_inner._role_org_id, role_permissions.restrict_object_type, 'permission'::acl_grant_object_type, null
            from t_inner join role_permissions on t_inner.member_role_id = role_permissions.role_id
            where t_inner.grant_object_type = 'role'
            union all
            select t_inner.role_id, null, t_inner._role_org_id, null, 'role'::acl_grant_object_type, role_members.member_role_id
            from
                t_inner
                join role_members on t_inner.member_role_id = role_members.role_id
                join roles on role_members.member_role_id = roles.id
            where t_inner.grant_object_type = 'role' and roles.deleted_at is null
        ) "x"
    ),
    final_result_set as (
        select * from t
    ),
    deleted_permissions as (
        delete from _expanded_role_permissions where
        _expanded_role_permissions.role_id in (select role_id from roles_to_update)
        and not exists (
            select 1 from final_result_set
            where
                _expanded_role_permissions.role_id = final_result_set.role_id
                and _expanded_role_permissions.grant_object_type = final_result_set.grant_object_type
                and (
                    (_expanded_role_permissions.permission isnull and final_result_set.permission isnull)
                    or (_expanded_role_permissions.permission = final_result_set.permission)
                )
                and (
                    (_expanded_role_permissions.restrict_object_type isnull and final_result_set.restrict_object_type isnull)
                    or (_expanded_role_permissions.restrict_object_type = final_result_set.restrict_object_type)
                )
                and (
                    (_expanded_role_permissions.member_role_id isnull and final_result_set.member_role_id isnull)
                    or (_expanded_role_permissions.member_role_id = final_result_set.member_role_id)
                )
        )
        returning 1
    ),
    inserted_permissions as (
        insert into _expanded_role_permissions select * from final_result_set
        on conflict do nothing
        returning 1
    )
    select num_deleted.cnt + num_inserted.cnt into _out0
    from
        (select count(*) cnt from deleted_permissions) "num_deleted"
        join (select count(*) cnt from inserted_permissions) "num_inserted"
        on true;

    -- Rebuild the set of expanded ACLs for all objects which have an ACL on
    -- this role or any role which inherits from it.
    with
    acl_roles_to_update as (
        select member_role_id role_id from _expanded_role_permissions
        where (_arg_role_id is null or role_id = _arg_role_id) and grant_object_type = 'role'
        -- Note that we still need to explicitly include the input role in case
        -- it was deleted, so it's no longer present in
        -- _expanded_role_permissions, but we still need to update the ACLs
        -- which reference it.
        union all
        select _arg_role_id role_id where _arg_role_id is not null
    ),
    acl_objects_to_update as (
        select distinct acls.object_type, acls.object_id
        from acls join acl_roles_to_update on acls.role_id = acl_roles_to_update.role_id
    ),
    updated_acls as (
        select refresh_expanded_acls_by_object(object_type, object_id) ret
        from acl_objects_to_update
    )
    select sum(ret) into _out1 from updated_acls;

    return _out0 + _out1;
end;
$$;

revoke execute on function refresh_expanded_role_permissions_by_role from public, anon;

-- Triggers for maintaining incrementally updated materialized expansions. These
-- are run after each statement, so they should run after the locking and
-- row-processing triggers installed above.

create function update_groups_trigger_f()
returns trigger
language plpgsql
security invoker
as $$
declare
    object_rec RECORD;
begin
    for object_rec in
        select distinct transition_tbl.id
        from transition_tbl
    loop
        perform refresh_expanded_group_members_by_group(object_rec.id);
    end loop;
    return null;
end;
$$;

revoke execute on function update_groups_trigger_f from public, anon;

create function update_roles_trigger_f()
returns trigger
language plpgsql
security invoker
as $$
declare
    object_rec RECORD;
begin
    for object_rec in
        select distinct transition_tbl.id
        from transition_tbl
    loop
        perform refresh_expanded_role_permissions_by_role(object_rec.id);
    end loop;
    return null;
end;
$$;

revoke execute on function update_roles_trigger_f from public, anon;

create function update_group_members_trigger_f()
returns trigger
language plpgsql
security invoker
as $$
declare
    object_rec RECORD;
begin
    for object_rec in
        select distinct transition_tbl.group_id
        from transition_tbl
    loop
        perform refresh_expanded_group_members_by_group(object_rec.group_id);
    end loop;
    return null;
end;
$$;

revoke execute on function update_group_members_trigger_f from public, anon;

create function update_role_members_trigger_f()
returns trigger
language plpgsql
security invoker
as $$
declare
    object_rec RECORD;
begin
    for object_rec in
        select distinct transition_tbl.role_id
        from transition_tbl
    loop
        perform refresh_expanded_role_permissions_by_role(object_rec.role_id);
    end loop;
    return null;
end;
$$;

revoke execute on function update_role_members_trigger_f from public, anon;

create function update_acls_trigger_f()
returns trigger
language plpgsql
security invoker
as $$
declare
    object_rec RECORD;
begin
    for object_rec in
        select distinct object_type, object_id
        from transition_tbl
    loop
        perform refresh_expanded_acls_by_object(
            object_rec.object_type, object_rec.object_id);
    end loop;
    return null;
end;
$$;

revoke execute on function update_acls_trigger_f from public, anon;

create trigger insert_groups_trigger
    after insert on groups
    referencing new table as transition_tbl
    for each statement execute procedure update_groups_trigger_f();
create trigger update_groups_old_trigger
    after update on groups
    referencing old table as transition_tbl
    for each statement execute procedure update_groups_trigger_f();
create trigger update_groups_new_trigger
    after update on groups
    referencing new table as transition_tbl
    for each statement execute procedure update_groups_trigger_f();
create trigger delete_groups_trigger
    after delete on groups
    referencing old table as transition_tbl
    for each statement execute procedure update_groups_trigger_f();

create trigger insert_group_users_trigger
    after insert on group_users
    referencing new table as transition_tbl
    for each statement execute procedure update_group_members_trigger_f();
create trigger delete_group_users_trigger
    after delete on group_users
    referencing old table as transition_tbl
    for each statement execute procedure update_group_members_trigger_f();
create trigger update_group_users_old_trigger
    after update on group_users
    referencing old table as transition_tbl
    for each statement execute procedure update_group_members_trigger_f();
create trigger update_group_users_new_trigger
    after update on group_users
    referencing new table as transition_tbl
    for each statement execute procedure update_group_members_trigger_f();

create trigger insert_group_members_trigger
    after insert on group_members
    referencing new table as transition_tbl
    for each statement execute procedure update_group_members_trigger_f();
create trigger delete_group_members_trigger
    after delete on group_members
    referencing old table as transition_tbl
    for each statement execute procedure update_group_members_trigger_f();
create trigger update_group_members_old_trigger
    after update on group_members
    referencing old table as transition_tbl
    for each statement execute procedure update_group_members_trigger_f();
create trigger update_group_members_new_trigger
    after update on group_members
    referencing new table as transition_tbl
    for each statement execute procedure update_group_members_trigger_f();

create trigger insert_roles_trigger
    after insert on roles
    referencing new table as transition_tbl
    for each statement execute procedure update_roles_trigger_f();
create trigger update_roles_old_trigger
    after update on roles
    referencing old table as transition_tbl
    for each statement execute procedure update_roles_trigger_f();
create trigger update_roles_new_trigger
    after update on roles
    referencing new table as transition_tbl
    for each statement execute procedure update_roles_trigger_f();
create trigger delete_roles_trigger
    after delete on roles
    referencing old table as transition_tbl
    for each statement execute procedure update_roles_trigger_f();

create trigger insert_role_permissions_trigger
    after insert on role_permissions
    referencing new table as transition_tbl for each statement
    execute procedure update_role_members_trigger_f();
create trigger delete_role_permissions_trigger
    after delete on role_permissions
    referencing old table as transition_tbl for each statement
    execute procedure update_role_members_trigger_f();
create trigger update_role_permissions_old_trigger
    after update on role_permissions
    referencing old table as transition_tbl for each statement
    execute procedure update_role_members_trigger_f();
create trigger update_role_permissions_new_trigger
    after update on role_permissions
    referencing new table as transition_tbl
    for each statement execute procedure update_role_members_trigger_f();

create trigger insert_role_members_trigger
    after insert on role_members
    referencing new table as transition_tbl
    for each statement execute procedure update_role_members_trigger_f();
create trigger delete_role_members_trigger
    after delete on role_members
    referencing old table as transition_tbl
    for each statement execute procedure update_role_members_trigger_f();
create trigger update_role_members_old_trigger
    after update on role_members
    referencing old table as transition_tbl
    for each statement execute procedure update_role_members_trigger_f();
create trigger update_role_members_new_trigger
    after update on role_members
    referencing new table as transition_tbl
    for each statement execute procedure update_role_members_trigger_f();

-- We specifically skip triggering the locked ACLs refresh for insert
-- statements, because the new ACLs are inserted into the _expanded_acls table
-- by the `register_acl_unchecked` function without needing to acquire the lock.
create trigger delete_acls_trigger
    after delete on acls
    referencing old table as transition_tbl for each statement
    execute procedure update_acls_trigger_f();
create trigger update_acls_old_trigger
    after update on acls
    referencing old table as transition_tbl for each statement
    execute procedure update_acls_trigger_f();
create trigger update_acls_new_trigger
    after update on acls
    referencing new table as transition_tbl
    for each statement execute procedure update_acls_trigger_f();

-- END SECTION RBAC SCHEMA

-- BEGIN SECTION RBAC SYSTEM GROUPS

-- For every organization, we maintain a set of "system" groups which are
-- associated to "system" roles and granted at the organization level. These
-- groups are useful to add new members to as they are invited.

create function get_system_role_id(_role_name text)
returns uuid
language sql
security invoker
stable
as $$
    select id from roles where org_id is null and name = _role_name and deleted_at is null;
$$;

revoke execute on function get_system_role_id from public, anon;

create function get_owner_role_id()
returns uuid
language sql
security invoker
stable
as $$
    select get_system_role_id('Owner')
$$;

revoke execute on function get_owner_role_id from public, anon;

create function get_group_id(_org_id uuid, _group_name text)
returns uuid
language sql
security invoker
stable
as $$
    select id from groups where org_id = _org_id and name = _group_name and deleted_at is null;
$$;

revoke execute on function get_group_id from public, anon;

create function create_system_groups(_org_id uuid)
returns void
language plpgsql
security invoker
as $$
begin
    -- Owners
    insert into groups(org_id, name)
    values (_org_id, 'Owners')
    on conflict do nothing;
    perform register_acl_unchecked(
        _object_type => 'organization', _object_id => _org_id,
        _group_id => get_group_id(_org_id, 'Owners'), _role_id => get_system_role_id('Owner'));

    -- Engineers
    insert into groups(org_id, name)
    values (_org_id, 'Engineers')
    on conflict do nothing;
    perform register_acl_unchecked(
        _object_type => 'org_project', _object_id => _org_id,
        _group_id => get_group_id(_org_id, 'Engineers'), _role_id => get_system_role_id('Engineer'));

    -- Viewers
    insert into groups(org_id, name)
    values (_org_id, 'Viewers')
    on conflict do nothing;
    perform register_acl_unchecked(
        _object_type => 'org_project', _object_id => _org_id,
        _group_id => get_group_id(_org_id, 'Viewers'), _role_id => get_system_role_id('Viewer'));
end;
$$;

revoke execute on function create_system_groups from public, anon;

create function create_system_groups_trigger_f()
returns trigger
language plpgsql
security invoker
as $$
begin
    perform create_system_groups(new.id);
    return new;
end;
$$;

revoke execute on function create_system_groups_trigger_f from public, anon;

create trigger create_system_groups_new_org_trigger
    after insert on organizations
    for each row execute procedure create_system_groups_trigger_f();

-- END SECTION RBAC SYSTEM GROUPS

-- BEGIN SECTION RBAC APPLICATION LOGIC

-- Functions for checking ACLs on individual objects. It is generally preferable
-- to put ACL checks into the query directly as joins against _expanded_acls,
-- but for certain use-cases (e.g. udfs for registering a project/experiment),
-- it is more convenient to run a single ACL check as its own statement.

create function has_organization_acl(
    _object_type acl_object_type,
    _org_id uuid,
    _permission permission_type,
    _user_id uuid)
returns boolean
language sql
security invoker
stable
as $$
select exists(
    select 1 from _expanded_acls
    where (
        object_type = 'organization'
        and object_id = _org_id
        and user_object_type = 'user'
        and user_group_id = _user_id
        and permission = _permission
        and (restrict_object_type is null or restrict_object_type = _object_type)
    )
);
$$;

revoke execute on function has_organization_acl from public, anon;

create function has_under_organization_acl(
    _object_type acl_object_type,
    _object_id uuid,
    _org_id uuid,
    _permission permission_type,
    _user_id uuid)
returns boolean
language sql
security invoker
stable
as $$
select exists(
    select 1 from _expanded_acls
    where (
        (
            object_type = _object_type
            and object_id = _object_id
            and user_object_type = 'user'
            and user_group_id = _user_id
            and permission = _permission
            and (restrict_object_type is null or restrict_object_type = _object_type)
        )
        or (
            object_type = 'organization'
            and object_id = _org_id
            and user_object_type = 'user'
            and user_group_id = _user_id
            and permission = _permission
            and (restrict_object_type is null or restrict_object_type = _object_type)
        )
    )
);
$$;

revoke execute on function has_under_organization_acl from public, anon;

create function has_project_acl(
    _object_type acl_object_type,
    _project_id uuid,
    _permission permission_type,
    _user_id uuid)
returns boolean
language sql
security invoker
stable
as $$
select exists(
    select 1 from projects
    where
        projects.id = _project_id
        and exists (
            select 1 from _expanded_acls
            where (
                (
                    object_type = 'project'
                    and object_id = _project_id
                    and user_object_type = 'user'
                    and user_group_id = _user_id
                    and permission = _permission
                    and (restrict_object_type is null or restrict_object_type = _object_type)
                )
                or (
                    object_type = 'org_project'
                    and object_id = projects.org_id
                    and user_object_type = 'user'
                    and user_group_id = _user_id
                    and permission = _permission
                    and (restrict_object_type is null or restrict_object_type = _object_type)
                )
                or (
                    object_type = 'organization'
                    and object_id = projects.org_id
                    and user_object_type = 'user'
                    and user_group_id = _user_id
                    and permission = _permission
                    and (restrict_object_type is null or restrict_object_type = _object_type)
                )
            )
        )
);
$$;

revoke execute on function has_project_acl from public, anon;

create function has_under_project_acl(
    _object_type acl_object_type,
    _object_id uuid,
    _project_id uuid,
    _permission permission_type,
    _user_id uuid)
returns boolean
language sql
security invoker
stable
as $$
select exists(
    select 1 from projects
    where
        projects.id = _project_id
        and exists (
            select 1 from _expanded_acls
            where (
                (
                    object_type = _object_type
                    and object_id = _object_id
                    and user_object_type = 'user'
                    and user_group_id = _user_id
                    and permission = _permission
                    and (restrict_object_type is null or restrict_object_type = _object_type)
                )
                or (
                    object_type = 'project'
                    and object_id = _project_id
                    and user_object_type = 'user'
                    and user_group_id = _user_id
                    and permission = _permission
                    and (restrict_object_type is null or restrict_object_type = _object_type)
                )
                or (
                    object_type = 'org_project'
                    and object_id = projects.org_id
                    and user_object_type = 'user'
                    and user_group_id = _user_id
                    and permission = _permission
                    and (restrict_object_type is null or restrict_object_type = _object_type)
                )
                or (
                    object_type = 'organization'
                    and object_id = projects.org_id
                    and user_object_type = 'user'
                    and user_group_id = _user_id
                    and permission = _permission
                    and (restrict_object_type is null or restrict_object_type = _object_type)
                )
            )
        )
);
$$;

revoke execute on function has_under_project_acl from public, anon;

create function register_role(
    auth_id uuid,
    org_id uuid,
    role_name text,
    description text default null,
    member_permissions permission_type[] default null,
    member_restrict_object_types acl_object_type[] default null,
    member_roles uuid[] default null,
    update boolean default null)
returns jsonb
language plpgsql
security invoker
as $$
declare
    _org_id uuid = org_id;
    _user_id uuid = get_user_id_by_auth_id(auth_id);
    _name text = role_name;
    _description text = description;
    _member_permissions permission_type[] = coalesce(member_permissions, '{}');
    _member_restrict_object_types acl_object_type[] = coalesce(member_restrict_object_types, '{}');
    _member_roles uuid[] = coalesce(member_roles, '{}');
    _update boolean = coalesce(update, false);

    _role_id uuid;
begin
    -- This function should not be used for registering system roles. So org_id
    -- must not be null.
    if _org_id is null then
        raise exception 'Must specify a non-null org_id when registering a role';
    end if;

    -- Since 'member_permissions' and 'member_restrict_object_types' are
    -- coupled, the arrays must have the same length.
    if array_length(_member_permissions, 1) <> array_length(_member_restrict_object_types, 1) then
        raise exception 'member_permissions array must have the same length as member_restrict_object_types';
    end if;

    -- Search for a matching existing role.
    select id into _role_id
    from roles where roles.org_id = _org_id and name = _name and deleted_at is null;

    if _role_id is not null then
        if _update then
          -- Replace the contents of the existing role, including permissions and
          -- inheritors.
          if not has_under_organization_acl('role', _role_id, _org_id, 'update', _user_id) then
              raise exception '%', jsonb_build_object('kind', 'not-found', 'permission', 'update', 'objectType', 'role', 'objectId', _role_id);
          end if;

          -- Update roles, permissions, and members with our given contents.

          update roles
          set name = _name, description = _description
          where id = _role_id;

          delete from role_permissions where role_id = _role_id;
          insert into role_permissions(role_id, permission, restrict_object_type)
          select _role_id, permission, restrict_object_type
          from unnest(_member_permissions, _member_restrict_object_types) t(permission, restrict_object_type);

          delete from role_members where role_id = _role_id;
          insert into role_members(role_id, member_role_id)
          select _role_id, member_role_id from unnest(_member_roles) member_role_id;
        else
          -- They must have read permission on the role.
          if not has_under_organization_acl('role', _role_id, _org_id, 'read', _user_id) then
              raise exception '%', jsonb_build_object('kind', 'not-found', 'permission', 'read', 'objectType', 'role', 'objectId', _role_id);
          end if;
        end if;
    else
        if not has_organization_acl('role', _org_id, 'create', _user_id) then
            raise exception '%', jsonb_build_object('kind', 'not-found', 'permission', 'create role', 'objectType', 'organization', 'objectId', _org_id);
        end if;

        -- Add roles, permissions, and inheritors with our given contents.

        insert into roles(org_id, user_id, name, description)
        values (_org_id, _user_id, _name, _description)
        returning id into _role_id;

        insert into role_permissions(role_id, permission, restrict_object_type)
        select _role_id, permission, restrict_object_type
        from unnest(_member_permissions, _member_restrict_object_types) t(permission, restrict_object_type);

        insert into role_members(role_id, member_role_id)
        select _role_id, member_role_id from unnest(_member_roles) member_role_id;

        perform register_acl_unchecked(
            _object_type => 'role', _object_id => _role_id,
            _user_id => _user_id, _role_id => get_owner_role_id());
    end if;

    -- The additional projections are copied from
    -- app/pages/api/role/_constants.ts.
    return (
        select jsonb_build_object('role', t)
        from (
            select
                *,
                (select coalesce(array_agg(jsonb_build_object('permission', permission::text, 'restrict_object_type', restrict_object_type::text))::jsonb[], '{}') member_permissions from role_permissions where role_permissions.role_id = roles.id),
                (select coalesce(array_agg(role_members.member_role_id)::uuid[], '{}') member_roles from role_members join roles "_joined_roles" on role_members.member_role_id = "_joined_roles".id where "_joined_roles".deleted_at isnull and role_members.role_id = roles.id)
            from
                roles
            where
                roles.id = _role_id
        ) t
    );
end;
$$;

revoke execute on function register_role from public, anon;

create function register_group(
    auth_id uuid,
    org_id uuid,
    group_name text,
    description text default null,
    member_users uuid[] default null,
    member_groups uuid[] default null,
    update boolean default null)
returns jsonb
language plpgsql
security invoker
as $$
declare
    _org_id uuid = org_id;
    _user_id uuid = get_user_id_by_auth_id(auth_id);
    _name text = group_name;
    _description text = description;
    _member_users uuid[] = coalesce(member_users, '{}');
    _member_groups uuid[] = coalesce(member_groups, '{}');
    _update boolean = coalesce(update, false);

    _group_id uuid;
begin
    -- Search for a matching existing group.
    select id into _group_id
    from groups where groups.org_id = _org_id and name = _name and deleted_at is null;

    if _group_id is not null then
        if _update then
          -- Replace the contents of the existing group, including users and
          -- inheritors.
          if not has_under_organization_acl('group', _group_id, _org_id, 'update', _user_id) then
              raise exception '%', jsonb_build_object('kind', 'not-found', 'permission', 'update', 'objectType', 'group', 'objectId', _group_id);
          end if;

          -- Update groups, users, and inheritors with our given contents.

          update groups
          set name = _name, description = _description
          where id = _group_id;

          delete from group_users where group_id = _group_id;
          insert into group_users(group_id, user_id)
          select _group_id, user_id from unnest(_member_users) user_id;

          delete from group_members where group_id = _group_id;
          insert into group_members(group_id, member_group_id)
          select _group_id, member_group_id from unnest(_member_groups) member_group_id;
        else
          -- They must have read permission on the group.
          if not has_under_organization_acl('group', _group_id, _org_id, 'read', _user_id) then
              raise exception '%', jsonb_build_object('kind', 'not-found', 'permission', 'read', 'objectType', 'group', 'objectId', _group_id);
          end if;
        end if;
    else
        if not has_organization_acl('group', _org_id, 'create', _user_id) then
            raise exception '%', jsonb_build_object('kind', 'not-found', 'permission', 'create group', 'objectType', 'organization', 'objectId', _org_id);
        end if;

        -- Add groups, users, and inheritors with our given contents.

        insert into groups(org_id, user_id, name, description)
        values (_org_id, _user_id, _name, _description)
        returning id into _group_id;

        insert into group_users(group_id, user_id)
        select _group_id, user_id from unnest(_member_users) user_id;

        insert into group_members(group_id, member_group_id)
        select _group_id, member_group_id from unnest(_member_groups) member_group_id;

        perform register_acl_unchecked(
            _object_type => 'group', _object_id => _group_id,
            _user_id => _user_id, _role_id => get_owner_role_id());
    end if;

    -- The additional projections are copied from
    -- app/pages/api/group/_constants.ts.
    return (
        select jsonb_build_object('group', t)
        from (
            select
                *,
                (select coalesce(array_agg(user_id)::uuid[], '{}') member_users from group_users where group_users.group_id = groups.id),
                (select coalesce(array_agg(member_group_id)::uuid[], '{}') member_groups from group_members join groups "_joined_groups" on group_members.member_group_id = "_joined_groups".id where "_joined_groups".deleted_at isnull and group_members.group_id = groups.id)
            from
                groups
            where
                groups.id = _group_id
        ) t
    );
end;
$$;

revoke execute on function register_group from public, anon;

create function find_acl_id(
    _object_type acl_object_type,
    _object_id uuid,
    _user_id uuid default null,
    _group_id uuid default null,
    _permission permission_type default null,
    _role_id uuid default null,
    _restrict_object_type acl_object_type default null)
returns uuid
language plpgsql
stable
as $$
declare
    _user_id_comparator text :=
        case when _user_id isnull
        then '(user_id isnull and $1 isnull)'
        else '(user_id = $1)'
        end;
    _group_id_comparator text :=
        case when _group_id isnull
        then '(group_id isnull and $2 isnull)'
        else '(group_id = $2)'
        end;
    _permission_comparator text :=
        case when _permission isnull
        then '(permission isnull and $3 isnull)'
        else '(permission = $3)'
        end;
    _role_id_comparator text :=
        case when _role_id isnull
        then '(role_id isnull and $4 isnull)'
        else '(role_id = $4)'
        end;
    _restrict_object_type_comparator text :=
        case when _restrict_object_type isnull
        then '(restrict_object_type isnull and $5 isnull)'
        else '(restrict_object_type = $5)'
        end;

    _user_object_type acl_user_object_type;
    _grant_object_type acl_grant_object_type;
    _ret uuid;
begin
    if (_user_id is null) = (_group_id is null) then
        raise exception 'Exactly one of user_id and group_id must be non-null';
    elsif _user_id is null then
        _user_object_type = 'group';
    else
        _user_object_type = 'user';
    end if;

    if (_permission is null) = (_role_id is null) then
        raise exception 'Exactly one of permission and role_id must be non-null';
    elsif _permission is null then
        _grant_object_type = 'role';
    else
        _grant_object_type = 'permission';
    end if;

    execute (
        'select id from acls where' ||
        concat_ws(' and ', _user_id_comparator, _group_id_comparator,
                  _permission_comparator, _role_id_comparator,
                  _restrict_object_type_comparator) ||
        ' and object_type = $6 and object_id = $7 and user_object_type = $8' ||
        ' and grant_object_type = $9'
    )
    into _ret
    using _user_id, _group_id, _permission, _role_id, _restrict_object_type,
          _object_type, _object_id, _user_object_type, _grant_object_type;
    return _ret;
end;
$$;

revoke execute on function find_acl_id from public, anon;

-- NOTE: the permissions requirements for registering an ACL differ based on
-- context, so we leave it up to the user.
--
-- If the ACL already exists, returns the existing one.
create function register_acl_unchecked(
    _object_type acl_object_type,
    _object_id uuid,
    _user_id uuid default null,
    _group_id uuid default null,
    _permission permission_type default null,
    _role_id uuid default null,
    _restrict_object_type acl_object_type default null)
returns jsonb
language plpgsql
security invoker
as $$
declare
    _acl_id uuid;
    _user_object_type acl_user_object_type;
    _grant_object_type acl_grant_object_type;

    _ret_obj jsonb;
begin
    if (_user_id is null) = (_group_id is null) then
        raise exception 'Exactly one of user_id and group_id must be non-null';
    elsif _user_id is null then
        _user_object_type = 'group';
    else
        _user_object_type = 'user';
    end if;

    if (_permission is null) = (_role_id is null) then
        raise exception 'Exactly one of permission and role_id must be non-null';
    elsif _permission is null then
        _grant_object_type = 'role';
    else
        _grant_object_type = 'permission';
    end if;

    insert into acls(
        object_type, object_id, user_object_type, user_id, group_id,
        grant_object_type, permission, role_id, restrict_object_type)
    values (
        _object_type, _object_id, _user_object_type, _user_id, _group_id,
        _grant_object_type, _permission, _role_id, _restrict_object_type)
    on conflict do nothing
    returning id into _acl_id;

    if _acl_id is null then
        -- Duplicate key. Just find the ID.
        select find_acl_id(
            _object_type, _object_id, _user_id, _group_id, _permission, _role_id,
            _restrict_object_type) into _acl_id
        ;
        if not found then
            raise exception 'Acl likely deleted concurrently with creation';
        end if;
    else
        -- It was a new ACL. Insert the expanded acls directly in this
        -- operation, rather than taking a lock and recomputing them from
        -- scratch.
        --
        -- Query generated from the following command:
        --
        -- ./scripts/make_expanded_acls_query.py "id = _acl_id"
        with
        candidate_acls as (
            select *
            from acls
            where id = _acl_id
        ),
        joined_acls as (
            select
                candidate_acls.*,
                _expanded_group_members.user_object_type expanded_user_object_type,
                _expanded_group_members.user_group_id expanded_user_group_id,
                _expanded_role_permissions.permission expanded_permission,
                _expanded_role_permissions.restrict_object_type expanded_restrict_object_type
            from
                candidate_acls
                    left join _expanded_group_members using (group_id)
                    left join _expanded_role_permissions using (role_id)
        ),
        coalesced_acls as (
        select
            id acl_id,
            object_type,
            object_id,
            coalesce(expanded_user_object_type, user_object_type) as user_object_type,
            coalesce(expanded_user_group_id, user_id) as user_group_id,
            coalesce(expanded_permission, permission) as permission,
            coalesce(expanded_restrict_object_type, restrict_object_type) as restrict_object_type,
            _object_org_id
        from
            joined_acls
        ),
        filtered_acls as (
            select * from coalesced_acls
            where
                -- It is possible that the user specifies an empty group or role, in
                -- which case we don't need to include these entries in the expanded
                -- ACLs.
                user_object_type is not null
                and user_group_id is not null
                and permission is not null
        )
        insert into _expanded_acls select * from filtered_acls on conflict do nothing;
    end if;

    return (
        select jsonb_build_object('acl', acls)
        from acls where id = _acl_id
    );
end;
$$;

revoke execute on function register_acl_unchecked from public, anon;

-- END SECTION RBAC APPLICATION LOGIC

create or replace function register_project_score(
    auth_id uuid,
    project_id uuid,
    project_score_name text,
    description text,
    score_type score_type,
    categories jsonb,
    config jsonb,
    update boolean)
returns jsonb
language plpgsql
security definer
as $$
declare
    _user_id uuid = get_user_id_by_auth_id(auth_id);
    _project_score_id uuid;
    _found_existing boolean = false;
begin
    if not has_project_acl('project', project_id, 'update', _user_id) then
        raise exception '%', jsonb_build_object('kind', 'not-found', 'permission', 'update', 'objectType', 'project', 'objectId', project_id);
    end if;

    insert into project_scores(project_id, user_id, name, description, score_type, categories, config)
    values (project_id, _user_id, project_score_name, description, score_type, categories, config)
    on conflict do nothing
    returning id into _project_score_id
    ;

    if _project_score_id is null then
      -- Duplicate key.
      _found_existing := true;

      select id
      from project_scores
      where
          project_scores.project_id = register_project_score.project_id
          and name = register_project_score.project_score_name
      into _project_score_id
      ;
      if not found then
          raise exception 'Project score % likely deleted concurrently with creation', project_score_name;
      end if;

      if update then
        update project_scores
        set
            user_id = _user_id,
            name = register_project_score.project_score_name,
            description = register_project_score.description,
            score_type = register_project_score.score_type,
            categories = register_project_score.categories,
            config = register_project_score.config
        where id = _project_score_id;
      end if;
    end if;

    return (
        select jsonb_build_object('project_score', project_scores, 'found_existing', _found_existing)
        from project_scores
        where project_scores.id = _project_score_id
    );
end;
$$;

revoke execute on function register_project_score from public, anon;

create function register_project_tag(
    auth_id uuid,
    project_id uuid,
    project_tag_name text,
    description text,
    color text,
    update boolean)
returns jsonb
language plpgsql
security definer
as $$
declare
    _user_id uuid = get_user_id_by_auth_id(auth_id);
    _project_tag_id uuid;
    _found_existing boolean = false;
begin
    if not has_project_acl('project', project_id, 'update', _user_id) then
        raise exception '%', jsonb_build_object('kind', 'not-found', 'permission', 'update', 'objectType', 'project', 'objectId', project_id);
    end if;

    insert into project_tags(project_id, user_id, name, description, color)
    values (project_id, _user_id, project_tag_name, description, color)
    on conflict do nothing
    returning id into _project_tag_id
    ;

    if _project_tag_id is null then
      -- Duplicate key.
      _found_existing := true;

      select id
      from project_tags
      where
          project_tags.project_id = register_project_tag.project_id
          and name = register_project_tag.project_tag_name
      into _project_tag_id
      ;
      if not found then
          raise exception 'Project tag % likely deleted concurrently with creation', project_tag_name;
      end if;

      if update then
        update project_tags
        set
            user_id = _user_id,
            name = register_project_tag.project_tag_name,
            description = register_project_tag.description,
            color = register_project_tag.color
        where id = _project_tag_id;
      end if;
    end if;

    return (
        select jsonb_build_object('project_tag', project_tags, 'found_existing', _found_existing)
        from project_tags
        where project_tags.id = _project_tag_id
    );
end;
$$;

revoke execute on function register_project_tag from public, anon;

create function register_span_iframe(
    auth_id uuid,
    project_id uuid,
    span_iframe_name text,
    description text,
    url text,
    post_message boolean,
    update boolean)
returns jsonb
language plpgsql
security definer
as $$
declare
    _user_id uuid = get_user_id_by_auth_id(auth_id);
    _span_iframe_id uuid;
    _found_existing boolean = false;
begin
    if not has_project_acl('project', project_id, 'update', _user_id) then
        raise exception '%', jsonb_build_object('kind', 'not-found', 'permission', 'update', 'objectType', 'project', 'objectId', project_id);
    end if;

    insert into span_iframes(project_id, user_id, name, description, url, post_message)
    values (project_id, _user_id, span_iframe_name, description, url, coalesce(post_message, false))
    on conflict do nothing
    returning id into _span_iframe_id
    ;

    if _span_iframe_id is null then
      -- Duplicate key.
      _found_existing := true;

      select id
      from span_iframes
      where
          span_iframes.project_id = register_span_iframe.project_id
          and name = register_span_iframe.span_iframe_name
      into _span_iframe_id
      ;
      if not found then
          raise exception 'Span iframe % likely deleted concurrently with creation', span_iframe_name;
      end if;

      if update then
        update span_iframes
        set
            user_id = _user_id,
            name = register_span_iframe.span_iframe_name,
            description = register_span_iframe.description,
            url = register_span_iframe.url,
            post_message = register_span_iframe.post_message
        where id = _span_iframe_id;
      end if;
    end if;

    return (
        select jsonb_build_object('span_iframe', span_iframes, 'found_existing', _found_existing)
        from span_iframes
        where span_iframes.id = _span_iframe_id
    );
end;
$$;

revoke execute on function register_span_iframe from public, anon;

create function register_org_secret(
    auth_id uuid,
    org_id uuid,
    org_secret_name text,
    type text default null,
    metadata jsonb default null,
    secret text default null,
    update boolean default null)
returns jsonb
language plpgsql
security definer
as $$
declare
    _org_id uuid = org_id;
    _user_id uuid = get_user_id_by_auth_id(auth_id);
    _name text = org_secret_name;
    _type text = type;
    _metadata jsonb = metadata;
    _secret text = secret;
    _update boolean = coalesce(update, false);

    _org_secret_id uuid;
    _found_existing boolean = false;
begin
    if not has_organization_acl('organization', org_id, 'update', _user_id) then
        raise exception '%', jsonb_build_object('kind', 'not-found', 'permission', 'update', 'objectType', 'organization', 'objectId', org_id);
    end if;

    insert into secrets.org_secrets(org_id, name, type, secret, metadata, updated_at)
    values (_org_id, _name, _type, _secret, _metadata, current_timestamp)
    on conflict do nothing
    returning id into _org_secret_id;

    if _org_secret_id is null then
      -- Duplicate key.
      _found_existing := true;

      select id from secrets.org_secrets
      where
          org_secrets.org_id = _org_id
          and org_secrets.name = _name
      into _org_secret_id;
      if not found then
        raise exception 'Org secret % likely deleted concurrently with creation', _name;
      end if;

      if _update then
        -- Replace the contents of the existing org secrets. We have to be
        -- careful to omit the 'secret' column entirely if it is null, so we
        -- write two separate SQL statements.
        if _secret is null then
          update secrets.org_secrets
          set type = _type, metadata = _metadata, updated_at = current_timestamp
          where id = _org_secret_id;
        else
          update secrets.org_secrets
          set type = _type, metadata = _metadata, secret = _secret, updated_at = current_timestamp
          where id = _org_secret_id;
        end if;
      end if;
    end if;

    return (
        select jsonb_build_object('org_secret', org_secrets, 'found_existing', _found_existing)
        from secrets.org_secrets
        where org_secrets.id = _org_secret_id
    );
end;
$$;

revoke execute on function register_org_secret from public, anon;

create type view_type as enum (
    'projects',
    'logs',
    'experiments',
    'datasets',
    'prompts',
    'playgrounds',
    'experiment',
    'dataset',
    'playground',
    'tools',
    'scorers',
    'monitor',
    'for_review'
);

create function valid_object_type_view_type_check(
    object_type acl_object_type, view_type view_type)
    returns boolean
    language plpgsql
    immutable parallel safe
as $$
begin
    return (select (
        case
          when object_type = 'org_project' then view_type in ('projects', 'monitor')
          when object_type = 'project' then view_type in (
              'logs',
              'experiments',
              'datasets',
              'prompts',
              'playgrounds',
              'experiment',
              'dataset',
              'playground',
              'tools',
              'scorers',
              'for_review'
          )
          when object_type = 'experiment' then view_type in ('experiment', 'for_review')
          when object_type = 'dataset' then view_type in ('dataset', 'for_review')
          else false
        end
    ));
end;
$$;

create table views (
    id uuid not null primary key default uuid_generate_v4(),
    object_type acl_object_type not null,
    object_id uuid not null,
    view_type view_type not null,
    name text not null,
    view_data_id uuid not null,
    options jsonb,
    created timestamp with time zone default current_timestamp,
    deleted_at timestamp with time zone,
    user_id uuid references users,
    constraint valid_object_type_view_type check (
        valid_object_type_view_type_check(object_type, view_type)
    )
);
alter table views enable row level security;

create unique index on views (object_type, object_id, view_type, name, deleted_at) nulls not distinct;

create function register_view_unchecked(
    auth_id uuid,
    object_type acl_object_type default null,
    object_id uuid default null,
    view_type view_type default null,
    name text default null,
    view_data_id uuid default null,
    options jsonb default null,
    update boolean default null)
returns jsonb
language plpgsql
security definer
as $$
declare
    -- Initialize meaningful defaults.
    _update boolean := coalesce(update, false);

    _user_id uuid := get_user_id_by_auth_id(auth_id);
    _view_id uuid;
    _found_existing boolean := false;
begin
    insert into views (object_type, object_id, view_type, name, view_data_id, options, user_id)
    values (
        register_view_unchecked.object_type,
        register_view_unchecked.object_id,
        register_view_unchecked.view_type,
        register_view_unchecked.name,
        register_view_unchecked.view_data_id,
        register_view_unchecked.options,
        _user_id
    )
    on conflict do nothing
    returning id into _view_id
    ;

    if _view_id is null then
        -- Duplicate key.
        _found_existing := true;
        select views.id
        from
            views
        where
            views.object_type = register_view_unchecked.object_type
            and views.object_id = register_view_unchecked.object_id
            and views.view_type = register_view_unchecked.view_type
            and views.name = register_view_unchecked.name
            and views.deleted_at isnull
        into _view_id
        ;
        if not found then
            raise exception 'View % (object_type %, object_id %, view_type %, name %) likely deleted concurrently with creation',
                register_view_unchecked.id, register_view_unchecked.object_type, register_view_unchecked.object_id, register_view_unchecked.view_type, register_view_unchecked.name;
        end if;

        if _update then
            update views
            set
                name = register_view_unchecked.name,
                view_data_id = register_view_unchecked.view_data_id,
                options = register_view_unchecked.options
            where id = _view_id;
        end if;
    end if;

    return (
        select jsonb_build_object('view', views, 'found_existing', _found_existing)
        from views
        where views.id = _view_id
    );
end;
$$;

revoke execute on function register_view_unchecked from public, anon;

-- Domain mapping from domain to org_id for new users.

create table domain_mappings (
    id uuid not null primary key default uuid_generate_v4(),
    domain text not null,
    created timestamp with time zone default current_timestamp,
    org_id uuid not null,
    group_id uuid,
    saml_group text,
    foreign key (group_id, org_id) references groups(id, org_id),
    foreign key (org_id) references organizations(id)
);

create unique index on domain_mappings (domain, saml_group, org_id, group_id) NULLS NOT DISTINCT;
alter table domain_mappings enable row level security;

-- Brainstore licenses for organizations.

create table brainstore_licenses (
  id uuid not null primary key DEFAULT uuid_generate_v4(),
  org_id uuid not null references organizations,
  created timestamp with time zone default current_timestamp,
  license text not null
);

alter table brainstore_licenses enable row level security;

create unique index on brainstore_licenses (license);

-- Telemetry (Usage Based Billing) for organizations.

create table if not exists org_billing (
    org_id uuid primary key references organizations,

    -- The URL to send telemetry data to. If null or empty, telemetry is disabled or relies
    -- on environment variables.
    telemetry_url text,

    -- The plan id of the current active subscription.
    plan_id text
);

alter table org_billing enable row level security;

create table organization_content_security_policies(
    org_id uuid not null primary key references organizations(id),
    policy text,
    policy_report_only text
);

alter table organization_content_security_policies enable row level security;

-- This table stores global configuration parameters for the db that we can
-- modify dynamically.
create table app_config(
    key text not null primary key,
    value_text text,
    value_boolean boolean
);

alter table app_config enable row level security;

create function get_app_config_boolean(key text)
returns boolean
language sql
security definer
as $$
select value_boolean from app_config where key = $1;
$$;

revoke execute on function get_app_config_boolean from public, anon;

create table project_automations (
    id uuid not null primary key default uuid_generate_v4(),
    project_id uuid not null references projects(id),
    user_id uuid not null references users(id),
    created timestamp with time zone default current_timestamp,

    name text not null,
    description text,
    config jsonb not null,

    event_type text generated always as (config->>'event_type') stored,
    object_type text generated always as (config->>'object_type') stored
);

create unique index project_automations_project_id_name_idx on project_automations(project_id, name);
create unique index project_automations_retention_unique_idx on project_automations (project_id, object_type) where event_type = 'retention';

alter table project_automations enable row level security;

create or replace function register_project_automation(
    auth_id uuid,
    project_id uuid,
    project_automation_name text,
    description text,
    config jsonb,
    update boolean)
returns jsonb
language plpgsql
security definer
as $$
declare
    _user_id uuid = get_user_id_by_auth_id(auth_id);
    _project_automation_id uuid;
    _found_existing boolean = false;
    _existing_retention_automation_name text;
begin
    if not has_project_acl('project', project_id, 'update', _user_id) then
        raise exception '%', jsonb_build_object('kind', 'not-found', 'permission', 'update', 'objectType', 'project', 'objectId', project_id);
    end if;

    insert into project_automations(
        project_id,
        user_id,
        name,
        description,
        config
    )
    values (
        project_id,
        _user_id,
        project_automation_name,
        description,
        config
    )
    on conflict do nothing
    returning id into _project_automation_id
    ;

    if _project_automation_id is null then
      -- Duplicate key.
      _found_existing := true;

      select id
      from project_automations
      where
          project_automations.project_id = register_project_automation.project_id
          and name = register_project_automation.project_automation_name
      into _project_automation_id
      ;

      -- secondary lookup on object_type unique constraint for better error message
      if not found and config->>'event_type' = 'retention' then
        select name
        from project_automations
        where
            project_automations.project_id = register_project_automation.project_id
            and project_automations.event_type = 'retention'
            and project_automations.object_type = register_project_automation.config->>'object_type'
        into _existing_retention_automation_name
        ;

        if found then
          raise exception '%', jsonb_build_object('kind', 'http-error', 'code', 400, 'message', format(
            'Project automation %s already exists with this retention configuration', _existing_retention_automation_name
          ));
        end if;
      elsif not found then
        raise exception 'Project automation % likely deleted concurrently with creation', project_automation_name;
      end if;

      if update then
        update project_automations
        set
            user_id = _user_id,
            name = register_project_automation.project_automation_name,
            description = register_project_automation.description,
            config = register_project_automation.config
        where id = _project_automation_id;
      end if;
    end if;

    return (
        select jsonb_build_object('project_automation', project_automations, 'found_existing', _found_existing)
        from project_automations
        where project_automations.id = _project_automation_id
    );
end;
$$;

revoke execute on function register_project_automation from public, anon;

create or replace function bt_service_account_user_id()
returns uuid language sql immutable parallel safe
as $$
    SELECT '6f87ebcc-7ea3-4f47-ae4c-e8f9514723e1'::uuid;
$$;

revoke execute on function bt_service_account_user_id from public, anon;

create or replace function bt_service_account_auth_id()
returns uuid language sql immutable parallel safe
as $$
    SELECT '8bd7e3e0-7b82-46a4-9d81-b58c5edeb281'::uuid;
$$;

revoke execute on function bt_service_account_auth_id from public, anon;

create or replace function bt_service_account_email()
returns text language sql immutable parallel safe
as $$
    SELECT 'bt::sp::bt_data_plane_manager-sysadmin';
$$;

revoke execute on function bt_service_account_email from public, anon;

-- Vercel integration metadata

create table vercel_installations (
  id uuid primary key default uuid_generate_v4(),
  -- Unique id per installation of the Vercel integration
  installation_id text not null unique,
  -- Unique id for installing Vercel account
  account_id text not null,
  -- For now account_id maps to a single org_id but we could allow users to connect
  -- multiple Braintrust orgs in the future
  org_id uuid not null references organizations(id),
  integration_type text not null check (integration_type in ('marketplace', 'external')),
  access_token text not null, -- encrypted at application layer using aes-gcm
  created timestamptz default current_timestamp
);

create index idx_vercel_installations_org_id on vercel_installations(org_id);
create index idx_vercel_installations_account_id on vercel_installations(account_id);

alter table vercel_installations enable row level security;

create table vercel_user_mappings (
  id uuid primary key default uuid_generate_v4(),
  user_id uuid not null references users(id),
  -- Unique id for this Vercel user / account / installation combination.
  -- This is not a globally unique identifier for the Vercel user which is why we need to
  -- maintain this mapping.
  vercel_user_id text not null,
  created timestamptz default current_timestamp,
  unique(vercel_user_id),
  unique(user_id, vercel_user_id)
);

create index idx_vercel_user_mappings_user_id on vercel_user_mappings(user_id);

alter table vercel_user_mappings enable row level security;
