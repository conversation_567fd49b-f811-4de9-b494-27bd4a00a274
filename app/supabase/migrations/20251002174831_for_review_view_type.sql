alter type view_type add value 'for_review';

create or replace function public.valid_object_type_view_type_check(object_type acl_object_type, view_type view_type)
  returns boolean
  language plpgsql
  immutable parallel safe
as $function$
begin
    return (select (
        case
          when object_type = 'org_project' then view_type in ('projects', 'monitor')
          when object_type = 'project' then view_type in (
              'logs',
              'experiments',
              'datasets',
              'prompts',
              'playgrounds',
              'experiment',
              'dataset',
              'playground',
              'tools',
              'scorers',
              'for_review'
          )
          when object_type = 'experiment' then view_type in ('experiment', 'for_review')
          when object_type = 'dataset' then view_type in ('dataset', 'for_review')
          else false
        end
    ));
end;
$function$
;
