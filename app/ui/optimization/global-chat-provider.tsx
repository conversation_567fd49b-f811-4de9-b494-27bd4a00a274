import React, {
  useState,
  useRef,
  useEffect,
  useMemo,
  use<PERSON><PERSON>back,
  useContext,
  type ReactNode,
} from "react";
import { produce, type Draft } from "immer";
import {
  GlobalChatContext,
  type ParsedMessage,
  type GlobalChatContextType,
  type ChatSession,
  type Mode,
  type UserMessage,
} from "./use-global-chat-context";
import {
  type ChatContext,
  type PageKey,
  buildSystemPrompt,
} from "@braintrust/local/optimization";
import { UIStateUpdatingLogger } from "./use-global-chat-context";
import {
  ALL_TOOL_NAMES,
  type EntityType,
  type ToolName,
} from "@braintrust/local/optimization/tools";
import {
  useOptimizationContext,
  type TaskEditConfirmationData,
  type DatasetEditConfirmationData,
  type UserConsentConfirmationData,
  type EditScorersConfirmationData,
  type CreateLLMScorerConfirmationData,
  type CreateCodeScorerConfirmationData,
  type DataSourceSelectionConfirmationData,
  type RunBtqlConfirmationData,
} from "#/utils/optimization/provider";
import { useEntityStorage } from "#/lib/clientDataStorage";
import { useEntityStorageBackedIndexedDB } from "#/utils/use-entity-storage-backed-indexeddb";
import { ProjectContext } from "#/app/app/[org]/p/[project]/projectContext";
import { type ContextObject } from "#/ui/optimization/use-global-chat-context";
import { usePathname } from "next/navigation";
import { useDebouncedCallback } from "#/utils/useDebouncedCallback";

import { useAvailableModels } from "#/ui/prompts/models";
import {
  isModelSupported,
  deriveDefaultModel,
  modelMaxOutputTokens,
  orderLoopAllowedModelsByPriority,
} from "#/ui/optimization/model-utils";
import { useOrg } from "#/utils/user";
import { useFeatureFlags } from "#/lib/feature-flags";
import { useQueryFunc } from "#/utils/react-query";
import { type fetchLoopModelWhitelist } from "#/app/app/[org]/settings/loop/actions";
import {
  isBTQLSandboxPage,
  isDatasetPage,
  isExperimentPage,
  isLogsPage,
  isLoopPage,
  isPlaygroundPage,
} from "#/app/app/[org]/pathname-checker";

export const INITIAL_LOOP_TOOLS: ToolName[] = Array.from(ALL_TOOL_NAMES);

const DEFAULT_CHAT_SESSIONS = {
  sessions: [],
};

interface GlobalChatProviderProps {
  children: ReactNode;
  isDefaultOpen?: boolean;
  getNewSessionOverrides?: () => Partial<ChatSession>;
  defaultDataSource?: {
    entity: EntityType;
    id: string;
  }[];
}

export function GlobalChatProvider({
  children,
  isDefaultOpen = false,
  getNewSessionOverrides,
  defaultDataSource,
}: GlobalChatProviderProps) {
  const { makeChatContext: makeOptimizationChatContext, tools } =
    useOptimizationContext();
  const projectContext = useContext(ProjectContext);
  const projectId = projectContext.projectId;
  const pathname = usePathname();

  const pageKey: PageKey = useMemo(() => {
    if (isPlaygroundPage(pathname ?? "")) return "playground";
    if (isExperimentPage(pathname ?? "")) return "experiments";
    if (isLogsPage(pathname ?? "")) return "logs";
    if (isDatasetPage(pathname ?? "")) return "dataset";
    if (isLoopPage(pathname ?? "")) return "loop";
    if (isBTQLSandboxPage(pathname ?? "")) return "btql";
    return "unknown";
  }, [pathname]);

  // if true, optimization chat will be undocked
  const [screenTooNarrow, setScreenTooNarrow] = useState(false);

  useEffect(() => {
    const shouldAutoUndock =
      pageKey === "experiments" ||
      pageKey === "dataset" ||
      pageKey === "logs" ||
      pageKey === "btql";

    //early return for pages that aren't width constrained.
    if (!shouldAutoUndock) return;

    const unDockWidth = shouldAutoUndock ? 1400 : 0;

    let timeoutId: NodeJS.Timeout | null = null;
    const throttledResize = () => {
      if (timeoutId) return;

      timeoutId = setTimeout(() => {
        const width = window.innerWidth;
        setScreenTooNarrow(shouldAutoUndock && width < unDockWidth);
        timeoutId = null;
      }, 100);
    };

    setScreenTooNarrow(shouldAutoUndock && window.innerWidth < unDockWidth);

    window.addEventListener("resize", throttledResize);

    return () => {
      window.removeEventListener("resize", throttledResize);
      if (timeoutId) clearTimeout(timeoutId);
    };
  }, [pageKey]);

  const { name: orgName, id: orgId } = useOrg();
  const implementedTools = tools.list();
  const { flags } = useFeatureFlags();

  const { configuredModelsByProvider } = useAvailableModels({ orgName });
  const availableModels = useMemo(
    () =>
      Object.entries(configuredModelsByProvider).flatMap(([provider, models]) =>
        models.map((model) => ({ ...model, provider })),
      ),
    [configuredModelsByProvider],
  );

  const { data: loopAllowedModels, isLoading: loopAllowedModelsLoading } =
    useQueryFunc<typeof fetchLoopModelWhitelist>({
      fName: "fetchLoopModelWhitelist",
      args: { orgId },
      queryOptions: {
        enabled: !!orgId,
        select: (data) => orderLoopAllowedModelsByPriority(data ?? []),
      },
    });

  const defaultModel = useMemo(
    () => deriveDefaultModel(availableModels, loopAllowedModels),
    [availableModels, loopAllowedModels],
  );
  const chatInstancesRef = useRef<
    Map<
      string,
      {
        chatInstance: ChatContext;
        logger: UIStateUpdatingLogger;
        abortController: AbortController;
      }
    >
  >(new Map());

  const [isChatOpen, setIsChatOpen] = useEntityStorage({
    entityType: "optimization",
    entityIdentifier: "",
    key: "isChatOpen",
    defaultValue: isDefaultOpen,
    onMount: true,
  });

  const [isAwaitingEditConfirmation, setIsAwaitingEditConfirmation] =
    useState(false);
  const [editTaskConfirmationData, setEditTaskConfirmationData] =
    useState<TaskEditConfirmationData | null>(null);
  const [editDatasetConfirmationData, setEditDatasetConfirmationData] =
    useState<DatasetEditConfirmationData | null>(null);
  const [userConsentConfirmationData, setUserConsentConfirmationData] =
    useState<UserConsentConfirmationData | null>(null);
  const [editScorersConfirmationData, setEditScorersConfirmationData] =
    useState<EditScorersConfirmationData | null>(null);
  const [createLLMScorerConfirmationData, setCreateLLMScorerConfirmationData] =
    useState<CreateLLMScorerConfirmationData | null>(null);
  const [
    createCodeScorerConfirmationData,
    setCreateCodeScorerConfirmationData,
  ] = useState<CreateCodeScorerConfirmationData | null>(null);
  const [
    dataSourceSelectionConfirmationData,
    setDataSourceSelectionConfirmationData,
  ] = useState<DataSourceSelectionConfirmationData | null>(null);
  const [runBtqlConfirmationData, setRunBtqlConfirmationData] =
    useState<RunBtqlConfirmationData | null>(null);
  const [activeSessionId, setActiveSessionId] = useState<string | null>(null);

  const [
    chatSessions,
    setChatSessions,
    _clearChatSessions,
    isChatSessionsLoaded,
  ] = useEntityStorageBackedIndexedDB({
    entityType: "optimization",
    entityIdentifier: `${projectId}-${pageKey}`,
    key: "chatSessions",
    defaultValue: DEFAULT_CHAT_SESSIONS,
  });
  const [currentChatSessionId, setCurrentChatSessionIdInLocalStorage] =
    useEntityStorage({
      entityType: "optimization",
      entityIdentifier: `${projectId}-${pageKey}`,
      key: "currentChatSessionId",
      defaultValue: "initial-chat-session",
    });
  const [isDocked, setIsDocked] = useEntityStorage({
    entityType: "optimization",
    entityIdentifier: `${projectId}-${pageKey}`,
    key: "isDocked",
    defaultValue: false,
  });

  const [model, setModel] = useEntityStorage({
    entityType: "optimization",
    entityIdentifier: orgName,
    key: "model",
    defaultValue: defaultModel,
  });

  const createNewChatSessionObject = useCallback(
    ({
      id,
      name,
      overrides = {},
    }: {
      id?: string;
      name?: string;
      overrides?: Partial<ChatSession>;
    }): ChatSession => {
      return {
        id: id ?? crypto.randomUUID(),
        name: name ?? "New Session",
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        messages: [],
        llmMessages: [],
        contextObjects: {},
        tools: implementedTools,
        mode: "agent",
        userMessage: "",
        hasInteractedWithContextObjects: false,
        ...overrides,
      };
    },
    [implementedTools],
  );

  const currentSession = useMemo(() => {
    if (chatSessions.sessions.length > 0) {
      return chatSessions.sessions.find(
        (session) => session.id === currentChatSessionId,
      );
    }
    return createNewChatSessionObject({ name: "New Session" });
  }, [chatSessions, currentChatSessionId, createNewChatSessionObject]);

  const getChatInstanceForSession = useCallback(
    (sessionId: string) => {
      if (!makeOptimizationChatContext) return null;
      if (!isChatSessionsLoaded) return null;
      let instance = chatInstancesRef.current.get(sessionId);
      if (!instance) {
        const logger = new UIStateUpdatingLogger(
          (updater: (draft: Draft<ParsedMessage>[]) => void) => {
            setChatSessions((prev) =>
              produce(prev, (draft) => {
                const session = draft.sessions.find((s) => s.id === sessionId);
                if (session) {
                  updater(session.messages);
                }
              }),
            );
          },
        );

        const maxOutputTokens = modelMaxOutputTokens(model, availableModels);
        const chatInstance = makeOptimizationChatContext({
          chatLogger: logger,
          allowed_tools: implementedTools,
          model: model,
          modelParams: {
            max_tokens: maxOutputTokens,
          },
          defaultSystemPrompt: buildSystemPrompt(pageKey, {
            defaultDataSource,
          }),
          messages: currentSession?.llmMessages,
          // Persist LLM message to the chat session, which enables session restoration and tab syncing
          onAddMessage: (message) => {
            setChatSessions((prev) => ({
              ...prev,
              sessions: prev.sessions.map((session) =>
                session.id === sessionId
                  ? {
                      ...session,
                      llmMessages: [...(session?.llmMessages ?? []), message],
                    }
                  : session,
              ),
            }));
          },
        });
        const abortController = new AbortController();

        instance = { chatInstance, logger, abortController };
        chatInstancesRef.current.set(sessionId, instance);
      }
      return instance;
    },
    [
      makeOptimizationChatContext,
      isChatSessionsLoaded,
      model,
      availableModels,
      implementedTools,
      pageKey,
      defaultDataSource,
      currentSession?.llmMessages,
      setChatSessions,
    ],
  );

  const currentChatInstance = useMemo(() => {
    return getChatInstanceForSession(currentChatSessionId);
  }, [getChatInstanceForSession, currentChatSessionId]);

  const setCurrentChatSessionId = useCallback(
    (sessionId: string) => {
      setCurrentChatSessionIdInLocalStorage(sessionId);
      const newSession = chatSessions.sessions.find((s) => s.id === sessionId);
      setUserMessageInput(newSession?.userMessage || "");
    },
    [setCurrentChatSessionIdInLocalStorage, chatSessions.sessions],
  );

  const createNewSession = useCallback(() => {
    const newSession = createNewChatSessionObject({
      name: "New Session",
      overrides: getNewSessionOverrides?.(),
    });
    setChatSessions((prevSessions) => ({
      ...prevSessions,
      sessions: [...prevSessions.sessions, newSession],
    }));
    setCurrentChatSessionId(newSession.id);
  }, [
    setChatSessions,
    setCurrentChatSessionId,
    createNewChatSessionObject,
    getNewSessionOverrides,
  ]);

  // Local state for user message input to minimize re-renders while typing
  const [userMessageInput, setUserMessageInput] = useState(
    currentSession?.userMessage || "",
  );
  const debouncedUpdateSessionUserMessage = useDebouncedCallback(
    (userMessage: string) => {
      if (currentChatSessionId) {
        setChatSessions((prev) => ({
          ...prev,
          sessions: prev.sessions.map((session) =>
            session.id === currentChatSessionId
              ? { ...session, userMessage }
              : session,
          ),
        }));
      }
    },
    1000,
  );
  const setCurrentSessionUserMessage = useCallback(
    (userMessage: string) => {
      setUserMessageInput(userMessage);
      debouncedUpdateSessionUserMessage(userMessage);
    },
    [debouncedUpdateSessionUserMessage],
  );

  const currentSessionMessages = useMemo(
    () => currentSession?.messages || [],
    [currentSession],
  );
  const currentSessionContextObjects = useMemo(
    () => currentSession?.contextObjects || {},
    [currentSession],
  );

  const currentSessionTools = useMemo(
    () => currentSession?.tools || implementedTools,
    [currentSession, implementedTools],
  );
  const currentSessionMode = currentSession?.mode ?? "agent";
  const currentSessionIsActive = activeSessionId === currentChatSessionId;
  const currentSessionHasInteractedWithContextObjects =
    currentSession?.hasInteractedWithContextObjects ?? false;

  const setCurrentSessionMessages = useCallback(
    (messages: ParsedMessage[]) => {
      if (currentChatSessionId)
        setChatSessions((prev) => ({
          ...prev,
          sessions: prev.sessions.map((session) =>
            session.id === currentChatSessionId
              ? { ...session, messages }
              : session,
          ),
        }));
    },
    [currentChatSessionId, setChatSessions],
  );

  const setCurrentSessionContextObjects = useCallback(
    (
      sessionContextObjects:
        | Record<string, ContextObject>
        | ((
            prev: Record<string, ContextObject>,
          ) => Record<string, ContextObject>),
    ) => {
      if (currentChatSessionId) {
        setChatSessions((prev) => ({
          ...prev,
          sessions: prev.sessions.map((session) =>
            session.id === currentChatSessionId
              ? {
                  ...session,
                  contextObjects:
                    typeof sessionContextObjects === "function"
                      ? sessionContextObjects(session.contextObjects)
                      : sessionContextObjects,
                }
              : session,
          ),
        }));
      }
    },
    [currentChatSessionId, setChatSessions],
  );

  const setCurrentSessionTools = useCallback(
    (tools: ToolName[] | ((prev: ToolName[]) => ToolName[])) => {
      if (currentChatSessionId)
        setChatSessions((prev) => ({
          ...prev,
          sessions: prev.sessions.map((session) =>
            session.id === currentChatSessionId
              ? {
                  ...session,
                  tools:
                    typeof tools === "function" ? tools(session.tools) : tools,
                }
              : session,
          ),
        }));
    },
    [currentChatSessionId, setChatSessions],
  );

  const setCurrentSessionMode = useCallback(
    (mode: Mode) => {
      if (currentChatSessionId)
        setChatSessions((prev) => ({
          ...prev,
          sessions: prev.sessions.map((session) =>
            session.id === currentChatSessionId
              ? { ...session, mode }
              : session,
          ),
        }));
    },
    [currentChatSessionId, setChatSessions],
  );

  const setCurrentSessionHasInteractedWithContextObjects = useCallback(
    (hasInteractedWithContextObjects: boolean) => {
      if (currentChatSessionId)
        setChatSessions((prev) => ({
          ...prev,
          sessions: prev.sessions.map((session) =>
            session.id === currentChatSessionId
              ? { ...session, hasInteractedWithContextObjects }
              : session,
          ),
        }));
    },
    [currentChatSessionId, setChatSessions],
  );

  const deleteSession = useCallback(
    (sessionId: string) => {
      setChatSessions((prev) => {
        const isCurrentSessionBeingDeleted = sessionId === currentChatSessionId;
        const filteredSessions = prev.sessions.filter(
          (s) => s.id !== sessionId,
        );

        // If we're deleting the current session and there are other sessions available
        if (isCurrentSessionBeingDeleted && filteredSessions.length > 0) {
          const deletedSessionIndex = prev.sessions.findIndex(
            (s) => s.id === sessionId,
          );

          // Switch to the previous session if possible, otherwise switch to the first session
          if (deletedSessionIndex > 0) {
            setCurrentChatSessionId(prev.sessions[deletedSessionIndex - 1].id);
          } else {
            setCurrentChatSessionId(filteredSessions[0].id);
          }
        }

        return {
          ...prev,
          sessions: filteredSessions,
        };
      });

      chatInstancesRef.current.delete(sessionId);
    },
    [setChatSessions, currentChatSessionId, setCurrentChatSessionId],
  );

  const handleAbort = useCallback(() => {
    const instance = chatInstancesRef.current.get(currentChatSessionId);
    if (instance) {
      instance.abortController.abort();
      // Create new abort controller for this session
      instance.abortController = new AbortController();
    }
    setActiveSessionId(null);
    setEditTaskConfirmationData(null);
    setEditDatasetConfirmationData(null);
    setUserConsentConfirmationData(null);
    setDataSourceSelectionConfirmationData(null);
    setRunBtqlConfirmationData(null);
  }, [
    currentChatSessionId,
    setActiveSessionId,
    setEditTaskConfirmationData,
    setEditDatasetConfirmationData,
    setUserConsentConfirmationData,
    setDataSourceSelectionConfirmationData,
    setRunBtqlConfirmationData,
  ]);

  const abortAllSessions = useCallback(() => {
    chatInstancesRef.current.forEach((instance) => {
      instance.abortController.abort();
      instance.abortController = new AbortController();
    });

    setActiveSessionId(null);
    setEditTaskConfirmationData(null);
    setEditDatasetConfirmationData(null);
    setUserConsentConfirmationData(null);
  }, [
    setActiveSessionId,
    setEditTaskConfirmationData,
    setEditDatasetConfirmationData,
    setUserConsentConfirmationData,
  ]);

  const handleSendMessage = useCallback(
    async (
      userMessage: UserMessage,
      options?: {
        clearContextObjects?: boolean;
        clearUserMessage?: boolean;
      },
    ) => {
      const sessionInstance = currentChatInstance;
      if (!sessionInstance) {
        return;
      }

      if (!userMessage.message.trim()) {
        return;
      }

      // Create new abort controller for this operation
      abortAllSessions();
      sessionInstance.abortController = new AbortController();
      const currentOperationController = sessionInstance.abortController;

      try {
        sessionInstance.logger.addUserMessage(
          userMessage.message,
          userMessage.contextObjects,
        );

        setActiveSessionId(currentChatSessionId);

        if (options?.clearContextObjects) {
          setCurrentSessionContextObjects({});
          if (Object.keys(userMessage.contextObjects ?? {}).length > 0) {
            setCurrentSessionHasInteractedWithContextObjects(true);
          }
        }
        if (options?.clearUserMessage) {
          setCurrentSessionUserMessage("");
        }

        setEditTaskConfirmationData(null);
        setEditDatasetConfirmationData(null);
        setUserConsentConfirmationData(null);
        setCreateLLMScorerConfirmationData(null);
        setCreateCodeScorerConfirmationData(null);

        let prompt = "";

        if (
          userMessage.contextObjects &&
          Object.keys(userMessage.contextObjects).length > 0
        ) {
          prompt = `Here are the tagged context:
          <context>
          ${JSON.stringify(userMessage.contextObjects)}
          </context>
          \n\n`;
        }
        prompt += userMessage.message;

        await sessionInstance.chatInstance.turn(prompt, {
          abortController: currentOperationController,
        });
      } catch (error) {
        if (error instanceof Error && error.name === "AbortError") {
          // silent handle
        } else {
          console.error("Error in handleSendMessage during chat.turn:", error);
          // Add error message to chat
          sessionInstance.logger.addSystemMessage(
            `Error: ${error instanceof Error ? error.message : "An unexpected error occurred"}`,
            "error",
          );
        }
      } finally {
        // This check is necessary because if the user kicked off a new turn, the abortcontroller will be different and we don't want to set that to false.
        if (currentOperationController === sessionInstance.abortController) {
          setActiveSessionId(null);
        }
        sessionInstance.logger.onTurnComplete();
      }
    },
    [
      currentChatInstance,
      abortAllSessions,
      currentChatSessionId,
      setCurrentSessionContextObjects,
      setCurrentSessionHasInteractedWithContextObjects,
      setCurrentSessionUserMessage,
    ],
  );

  useEffect(() => {
    const handlePageHide = () => {
      chatInstancesRef.current.forEach((instance) => {
        instance.abortController.abort();
      });
      chatInstancesRef.current.clear();
    };

    window.addEventListener("pagehide", handlePageHide);

    return () => {
      window.removeEventListener("pagehide", handlePageHide);
    };
  }, []);

  useEffect(() => {
    if (
      isChatSessionsLoaded &&
      chatSessions &&
      chatSessions.sessions &&
      chatSessions.sessions.length === 0 &&
      //we check for tools because we don't want to create a session if the tools are not ready yet. and OptimizationProvider will not have tools initially.
      implementedTools.length > 0
    ) {
      createNewSession();
    }
  }, [chatSessions, createNewSession, implementedTools, isChatSessionsLoaded]);

  // Update current session's chat instance configuration when tools/model change
  useEffect(() => {
    const instance = currentChatInstance;
    if (instance) {
      instance.chatInstance.reconfigure({
        allowed_tools: currentSessionTools,
        model: model,
        modelParams: {
          max_tokens: modelMaxOutputTokens(model, availableModels),
        },
      });
    }
  }, [currentChatInstance, currentSessionTools, model, availableModels]);

  useEffect(() => {
    const instance = currentChatInstance;
    if (instance && currentSession?.llmMessages && isChatSessionsLoaded) {
      instance.chatInstance.updateMessagesWithExternal(
        currentSession?.llmMessages,
      );
    }
  }, [currentChatInstance, currentSession?.llmMessages, isChatSessionsLoaded]);

  useEffect(() => {
    if (availableModels) {
      const modelIsSupported = isModelSupported(
        model,
        flags.loopTryOtherModels,
        availableModels,
        loopAllowedModels,
      );
      if (!modelIsSupported) {
        const newDefaultModel = deriveDefaultModel(
          availableModels,
          loopAllowedModels,
        );

        setModel(newDefaultModel ?? "");
      }
    }
  }, [
    availableModels,
    model,
    setModel,
    flags.loopTryOtherModels,
    loopAllowedModels,
  ]);

  const globalChatContextValue: GlobalChatContextType = useMemo(
    () => ({
      chat: currentChatInstance?.chatInstance || null,
      isAwaitingEditConfirmation,
      setIsAwaitingEditConfirmation,
      editTaskConfirmationData,
      setEditTaskConfirmationData,
      editDatasetConfirmationData,
      setEditDatasetConfirmationData,
      userConsentConfirmationData,
      setUserConsentConfirmationData,
      editScorersConfirmationData,
      setEditScorersConfirmationData,
      createLLMScorerConfirmationData,
      setCreateLLMScorerConfirmationData,
      createCodeScorerConfirmationData,
      setCreateCodeScorerConfirmationData,
      dataSourceSelectionConfirmationData,
      setDataSourceSelectionConfirmationData,
      runBtqlConfirmationData,
      setRunBtqlConfirmationData,
      isChatOpen,
      setIsChatOpen,
      isDocked: isDocked ?? false,
      setIsDocked,
      createNewSession,
      chatSessions,
      setChatSessions,
      currentChatSessionId,
      setCurrentChatSessionId,
      currentSessionMessages,
      currentSessionContextObjects,
      model,
      setModel,
      currentSessionTools,
      currentSessionMode,
      currentSessionIsActive,
      activeSessionId,
      currentSessionUserMessage: userMessageInput,
      currentSessionHasInteractedWithContextObjects,
      setCurrentSessionMessages,
      setCurrentSessionContextObjects,
      setCurrentSessionTools,
      setCurrentSessionMode,
      setCurrentSessionHasInteractedWithContextObjects,
      setCurrentSessionUserMessage,
      handleSendMessage,
      handleAbort,
      deleteSession,
      implementedTools,
      pageKey,
      screenTooNarrow,
      loopAllowedModels: loopAllowedModels ?? null,
      loopAllowedModelsLoading,
    }),
    [
      currentChatInstance,
      isAwaitingEditConfirmation,
      setIsAwaitingEditConfirmation,
      editTaskConfirmationData,
      setEditTaskConfirmationData,
      editDatasetConfirmationData,
      setEditDatasetConfirmationData,
      userConsentConfirmationData,
      setUserConsentConfirmationData,
      editScorersConfirmationData,
      setEditScorersConfirmationData,
      createLLMScorerConfirmationData,
      setCreateLLMScorerConfirmationData,
      createCodeScorerConfirmationData,
      setCreateCodeScorerConfirmationData,
      dataSourceSelectionConfirmationData,
      setDataSourceSelectionConfirmationData,
      runBtqlConfirmationData,
      setRunBtqlConfirmationData,
      isChatOpen,
      setIsChatOpen,
      isDocked,
      setIsDocked,
      createNewSession,
      chatSessions,
      setChatSessions,
      currentChatSessionId,
      setCurrentChatSessionId,
      currentSessionMessages,
      currentSessionContextObjects,
      model,
      setModel,
      currentSessionTools,
      currentSessionMode,
      currentSessionIsActive,
      userMessageInput,
      currentSessionHasInteractedWithContextObjects,
      setCurrentSessionMessages,
      setCurrentSessionContextObjects,
      setCurrentSessionTools,
      setCurrentSessionMode,
      setCurrentSessionHasInteractedWithContextObjects,
      setCurrentSessionUserMessage,
      handleSendMessage,
      handleAbort,
      deleteSession,
      implementedTools,
      pageKey,
      screenTooNarrow,
      loopAllowedModels,
      loopAllowedModelsLoading,
      activeSessionId,
    ],
  );

  return (
    <GlobalChatContext.Provider value={globalChatContextValue}>
      {children}
    </GlobalChatContext.Provider>
  );
}
