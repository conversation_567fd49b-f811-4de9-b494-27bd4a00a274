import { describe, it, expect } from "vitest";
import { otelSpanToRow } from "../collector";

/**
 * Test data for LiveKit OTEL telemetry processing.
 *
 * This data was captured from running a LiveKit realtime agent example.
 * The LiveKit library generates realtime assistant turn spans with livekit-specific attributes.
 */

describe("livekit span handler", () => {
  it("handles livekit realtime assistant turn span", () => {
    const livekitSpan = {
      traceId: "40a66081e6c24ffc7f394e730d230b9a",
      spanId: "1922c932d5bf6284",
      parentSpanId: "6f3a33261a3f1b44",
      name: "realtime_assistant_turn",
      startTimeUnixNano: "**********254939800",
      endTimeUnixNano: "1759426023027350000",
      attributes: [
        {
          key: "gen_ai.request.model",
          value: {
            stringValue: "gpt-realtime",
          },
        },
        {
          key: "gen_ai.usage.input_audio_tokens",
          value: {
            intValue: "233",
          },
        },
        {
          key: "gen_ai.usage.input_cached_tokens",
          value: {
            intValue: "0",
          },
        },
        {
          key: "gen_ai.usage.input_text_tokens",
          value: {
            intValue: "36",
          },
        },
        {
          key: "gen_ai.usage.input_tokens",
          value: {
            intValue: "269",
          },
        },
        {
          key: "gen_ai.usage.output_audio_tokens",
          value: {
            intValue: "101",
          },
        },
        {
          key: "gen_ai.usage.output_text_tokens",
          value: {
            intValue: "34",
          },
        },
        {
          key: "gen_ai.usage.output_tokens",
          value: {
            intValue: "135",
          },
        },
        {
          key: "lk.interrupted",
          value: {
            boolValue: false,
          },
        },
        {
          key: "lk.realtime_model_metrics",
          value: {
            stringValue:
              '{"type":"realtime_model_metrics","label":"livekit.plugins.openai.realtime.realtime_model.RealtimeModel","request_id":"resp_CMHMuhoMQjNBVqaYKQ3CI","timestamp":**********.254595,"duration":2.831033945083618,"ttft":1.6476109027862549,"cancelled":false,"input_tokens":269,"output_tokens":135,"total_tokens":404,"tokens_per_second":47.68575814304219,"input_token_details":{"audio_tokens":233,"text_tokens":36,"image_tokens":0,"cached_tokens":0,"cached_tokens_details":{"audio_tokens":0,"text_tokens":0,"image_tokens":0}},"output_token_details":{"text_tokens":34,"audio_tokens":101,"image_tokens":0},"metadata":{"model_name":"gpt-realtime","model_provider":"api.openai.com"}}',
          },
        },
        {
          key: "lk.response.function_calls",
          value: {
            stringValue: "[]",
          },
        },
        {
          key: "lk.response.text",
          value: {
            stringValue:
              "You're welcome! If you have any more questions or need help with anything, just let me know.",
          },
        },
        {
          key: "lk.speech_id",
          value: {
            stringValue: "speech_5336c0cd74a4",
          },
        },
        {
          key: "model",
          value: {
            stringValue: "gpt-realtime",
          },
        },
      ],
      status: {},
    };

    const result = otelSpanToRow(livekitSpan);
    if ("skipped" in result) {
      throw new Error("Span should not be skipped");
    }
    const { row, fieldStats } = result;

    expect(row).toMatchObject({
      output: [
        {
          role: "assistant",
          content:
            "You're welcome! If you have any more questions or need help with anything, just let me know.",
        },
      ],
      metadata: expect.objectContaining({
        "gen_ai.request.model": "gpt-realtime",
        model: "gpt-realtime",
        "lk.interrupted": false,
        "lk.response.function_calls": "[]",
        "lk.speech_id": "speech_5336c0cd74a4",
      }),
      metrics: expect.objectContaining({
        prompt_tokens: 269,
        completion_tokens: 135,
        tokens: 404,
        input_audio_tokens: 233,
        input_cached_tokens: 0,
        input_text_tokens: 36,
        output_audio_tokens: 101,
        output_text_tokens: 34,
      }),
      span_attributes: expect.objectContaining({
        name: "realtime_assistant_turn",
        type: "llm",
      }),
    });

    // Validate field processing stats - livekit should handle output and span_attributes
    expect(fieldStats.toObject()).toEqual({
      livekit: { ok: 2, errs: 0 },
      genAI: { ok: 2, errs: 0 },
    });
  });
});
