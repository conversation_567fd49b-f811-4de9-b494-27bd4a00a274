import { get } from "lodash";
import { SpanSpec, notHandled, handled } from "./attributes";

export const livekitSpanSpec: SpanSpec = {
  output: (attributes) => {
    // Extract lk.response.text from attributes
    const responseText = get(attributes, "lk.response.text");
    if (responseText === undefined || typeof responseText !== "string") {
      return notHandled;
    }

    return handled(
      {
        isLLM: true,
        data: [
          {
            role: "assistant" as const,
            content: responseText,
          },
        ],
      },
      ["lk.response.text"],
    );
  },
  span_attributes: (attributes) => {
    // Check if this is a livekit span by looking for lk.response.text
    const responseText = get(attributes, "lk.response.text");
    if (responseText === undefined) {
      return notHandled;
    }

    return handled({ type: "llm" });
  },
};
