name: Vercel Preview Deployment
on:
  pull_request:
    paths-ignore:
      - "terraform/**"
      - "api/**"
  workflow_dispatch:
    inputs:
      debug_enabled:
        type: boolean
        description: "Run the build with tmate debugging enabled"
        required: false
        default: false
env:
  VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
  VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID }}
  NODE_OPTIONS: ${{ vars.NODE_OPTIONS }}
  TURBO_TEAM: ${{ secrets.VERCEL_ORG_ID }}
  TURBO_TOKEN: ${{ secrets.VERCEL_TOKEN }}
jobs:
  Deploy-Preview:
    permissions:
      deployments: write
      pull-requests: write
      contents: read
    runs-on: warp-ubuntu-2404-x64-8x
    outputs:
      preview_url: ${{ steps.vercel-deploy.outputs.ALIAS_URL }}
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: ${{ env.FETCH_DEPTH }}
          submodules: recursive

      - uses: ./.github/actions/deps

      - name: Install Vercel CLI
        run: npm install --global vercel@latest
      - name: Pull Vercel Environment Information
        run: |
          vercel pull --yes --environment=preview --token=${{ secrets.VERCEL_TOKEN }}
          echo "NEXT_PUBLIC_COMMIT_SHA=$(git rev-parse --short HEAD)" >> .env
      - name: Build Project Artifacts
        run: vercel build --token=${{ secrets.VERCEL_TOKEN }}
      - name: Deploy Project Artifacts to Vercel
        id: vercel-deploy
        run: |
          BRANCH_NAME=$(echo ${GITHUB_HEAD_REF} | tr '[:upper:]' '[:lower:]' | sed 's/[^a-z0-9]/-/g' | cut -c1-63)
          DEPLOY_OUTPUT=$(vercel deploy --prebuilt --archive=tgz --token=${{ secrets.VERCEL_TOKEN }} --scope braintrustdata)
          echo "PREVIEW_URL=$DEPLOY_OUTPUT" >> $GITHUB_OUTPUT
          vercel alias set $DEPLOY_OUTPUT $BRANCH_NAME.preview.braintrust.dev --token=${{ secrets.VERCEL_TOKEN }} --scope braintrustdata
          echo "ALIAS_URL=https://$BRANCH_NAME.preview.braintrust.dev" >> $GITHUB_OUTPUT

      - name: Find Comment
        uses: peter-evans/find-comment@v3
        id: fc
        with:
          issue-number: ${{ github.event.pull_request.number }}
          comment-author: "github-actions[bot]"
          body-includes: Preview deployment is ready

      - name: Delete existing comment
        if: steps.fc.outputs.comment-id != ''
        run: |
          curl -X DELETE \
          -H "Authorization: token ${{ secrets.GITHUB_TOKEN }}" \
          "https://api.github.com/repos/${{ github.repository }}/issues/comments/${{ steps.fc.outputs.comment-id }}"

      - name: Create new comment
        uses: peter-evans/create-or-update-comment@v4
        with:
          body: |
            🚀 Preview deployment is ready! ${{ steps.vercel-deploy.outputs.ALIAS_URL }}
          token: ${{ secrets.GITHUB_TOKEN }}
          issue-number: ${{ github.event.pull_request.number }}

      - uses: ./.github/actions/terminal
        if: ${{ (success() || failure()) && github.event_name == 'workflow_dispatch' && inputs.debug_enabled }}

  notify-qawolf:
    name: Notify QA Wolf
    needs: Deploy-Preview
    runs-on: ubuntu-latest
    steps:
      - name: Notify QA Wolf of deployment
        uses: qawolf/notify-qawolf-on-deploy-action@v1
        env:
          GITHUB_TOKEN: "${{ secrets.GITHUB_TOKEN }}"
        with:
          qawolf-api-key: "${{ secrets.QAWOLF_API_KEY }}"
          deployment-type: "pr-testing-button"
          deployment-url: "${{ needs.Deploy-Preview.outputs.preview_url }}"
